#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书软广智能分析系统 - 基于平台特性优化版
专注于真正影响投放效果的关键因素
"""

import pandas as pd
import numpy as np
from collections import Counter
import re

class SmartXHSAnalyzer:
    def __init__(self):
        self.df = None
        self.processed_df = None
        
    def load_data(self, file_path):
        """加载Excel数据"""
        try:
            self.df = pd.read_excel(file_path)
            print(f"✅ 数据加载成功！共 {len(self.df)} 条记录")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def preprocess_data(self):
        """数据预处理"""
        if self.df is None:
            return False
            
        self.processed_df = self.df.copy()
        
        # 处理数值字段
        self._convert_wan_fields()
        self._calculate_core_metrics()
        self._classify_content()
        self._categorize_bloggers()
        
        print("✅ 数据预处理完成")
        return True
    
    def _convert_wan_fields(self):
        """处理包含'万'的数值字段"""
        def convert_wan_to_number(value):
            if pd.isna(value):
                return 0
            value_str = str(value)
            if '万' in value_str:
                number = float(value_str.replace('万', ''))
                return int(number * 10000)
            else:
                try:
                    return int(float(value_str))
                except:
                    return 0
        
        self.processed_df['粉丝_数值'] = self.processed_df['粉丝'].apply(convert_wan_to_number)
        self.processed_df['获赞与收藏_数值'] = self.processed_df['获赞与收藏'].apply(convert_wan_to_number)
        
        # 处理收藏数
        self.processed_df['收藏数_数值'] = self.processed_df['收藏数'].replace('收藏', 0)
        self.processed_df['收藏数_数值'] = pd.to_numeric(self.processed_df['收藏数_数值'], errors='coerce').fillna(0)
    
    def _calculate_core_metrics(self):
        """计算核心指标"""
        # 总互动数
        self.processed_df['总互动数'] = (
            self.processed_df['点赞数'] + 
            self.processed_df['收藏数_数值'] + 
            self.processed_df['评论数']
        )
        
        # 互动率 (关键指标)
        self.processed_df['互动率'] = np.where(
            self.processed_df['粉丝_数值'] > 0,
            self.processed_df['总互动数'] / self.processed_df['粉丝_数值'] * 100,
            0
        )
        
        # 用户行为偏好分析
        self.processed_df['点赞占比'] = np.where(
            self.processed_df['总互动数'] > 0,
            self.processed_df['点赞数'] / self.processed_df['总互动数'] * 100,
            0
        )
        
        self.processed_df['收藏占比'] = np.where(
            self.processed_df['总互动数'] > 0,
            self.processed_df['收藏数_数值'] / self.processed_df['总互动数'] * 100,
            0
        )
        
        self.processed_df['评论占比'] = np.where(
            self.processed_df['总互动数'] > 0,
            self.processed_df['评论数'] / self.processed_df['总互动数'] * 100,
            0
        )
        
        # 内容特征
        self.processed_df['标题长度'] = self.processed_df['软文标题'].str.len()
        self.processed_df['内容长度'] = self.processed_df['软文内容'].str.len()
    
    def _classify_content(self):
        """智能内容分类"""
        def identify_content_type(title, content):
            text = (title + ' ' + content).lower()
            
            # 测评避雷类
            if any(word in text for word in ['测评', '避雷', '踩雷', '不推荐', '差评', '不要', '别碰']):
                return '测评避雷'
            # 种草推荐类
            elif any(word in text for word in ['推荐', '好吃', '必备', '攻略', '合集', '好用']):
                return '种草推荐'
            # 工具介绍类
            elif any(word in text for word in ['程序', '小程序', 'app', '软件', '工具']):
                return '工具介绍'
            # 生活记录类
            elif any(word in text for word in ['总结', '记录', '日常', '每天', '生活']):
                return '生活记录'
            # 教程攻略类
            elif any(word in text for word in ['教程', '怎么', '如何', '方法', '步骤']):
                return '教程攻略'
            else:
                return '其他'
        
        self.processed_df['内容类型'] = self.processed_df.apply(
            lambda row: identify_content_type(row['软文标题'], row['软文内容']), axis=1
        )
        
        # 标题情感分析
        def analyze_title_emotion(title):
            positive_words = ['好吃', '推荐', '必备', '好用', '爱了', '绝了', '太好', '完美', '惊喜', '必须']
            negative_words = ['避雷', '不要', '别碰', '差', '坑', '难吃', '不推荐', '踩雷']
            urgent_words = ['限时', '抢', '最后', '马上', '立即', '快', '速度']
            
            has_positive = any(word in title for word in positive_words)
            has_negative = any(word in title for word in negative_words)
            has_urgent = any(word in title for word in urgent_words)
            
            if has_positive and has_urgent:
                return '正面+紧迫'
            elif has_negative and has_urgent:
                return '负面+紧迫'
            elif has_positive:
                return '正面情感'
            elif has_negative:
                return '负面情感'
            elif has_urgent:
                return '紧迫感'
            else:
                return '中性'
        
        self.processed_df['标题情感'] = self.processed_df['软文标题'].apply(analyze_title_emotion)
    
    def _categorize_bloggers(self):
        """博主分类 - 基于小红书实际情况"""
        def categorize_blogger(fans):
            if fans < 1000:
                return '素人博主(<1K)'
            elif fans < 5000:
                return '小博主(1K-5K)'
            elif fans < 20000:
                return '中博主(5K-2W)'
            elif fans < 100000:
                return '大博主(2W-10W)'
            else:
                return '头部博主(>10W)'
        
        self.processed_df['博主类型'] = self.processed_df['粉丝_数值'].apply(categorize_blogger)
    
    def get_investment_insights(self):
        """投放策略洞察"""
        if self.processed_df is None:
            return None
        
        insights = {}
        
        # 1. 博主选择策略
        blogger_performance = self.processed_df.groupby('博主类型').agg({
            '互动率': ['mean', 'std', 'count'],
            '点赞数': 'mean',
            '收藏数_数值': 'mean',
            '评论数': 'mean'
        }).round(2)
        
        insights['博主选择策略'] = {
            '最佳ROI博主类型': blogger_performance['互动率']['mean'].idxmax(),
            '各类型博主表现': blogger_performance
        }
        
        # 2. 内容类型策略
        content_performance = self.processed_df.groupby('内容类型').agg({
            '互动率': ['mean', 'count'],
            '点赞数': 'mean',
            '收藏数_数值': 'mean',
            '评论数': 'mean'
        }).round(2)
        
        insights['内容类型策略'] = {
            '最佳内容类型': content_performance['互动率']['mean'].idxmax(),
            '各类型内容表现': content_performance
        }
        
        # 3. 标题情感策略
        emotion_performance = self.processed_df.groupby('标题情感').agg({
            '互动率': ['mean', 'count'],
            '点赞数': 'mean',
            '收藏数_数值': 'mean',
            '评论数': 'mean'
        }).round(2)
        
        insights['标题情感策略'] = {
            '最佳情感类型': emotion_performance['互动率']['mean'].idxmax(),
            '各情感类型表现': emotion_performance
        }
        
        # 4. 地域投放策略
        if self.processed_df['IP属地'].notna().sum() > 0:
            regional_performance = self.processed_df.groupby('IP属地').agg({
                '互动率': ['mean', 'count'],
                '点赞数': 'mean',
                '收藏数_数值': 'mean',
                '评论数': 'mean'
            }).round(2)
            
            insights['地域投放策略'] = {
                '最佳投放地域': regional_performance['互动率']['mean'].idxmax(),
                '各地域表现': regional_performance
            }
        
        return insights
    
    def get_content_optimization_tips(self):
        """内容优化建议"""
        if self.processed_df is None:
            return None
        
        tips = []
        
        # 分析高效内容特征
        top_content = self.processed_df.nlargest(5, '互动率')
        
        tips.append("🏆 高效内容特征分析:")
        for idx, row in top_content.iterrows():
            tips.append(f"   《{row['软文标题']}》")
            tips.append(f"   - 互动率: {row['互动率']:.1f}%")
            tips.append(f"   - 内容类型: {row['内容类型']}")
            tips.append(f"   - 标题情感: {row['标题情感']}")
            tips.append(f"   - 博主类型: {row['博主类型']}")
            tips.append("")
        
        # 用户行为偏好
        avg_like_ratio = self.processed_df['点赞占比'].mean()
        avg_collect_ratio = self.processed_df['收藏占比'].mean()
        avg_comment_ratio = self.processed_df['评论占比'].mean()
        
        tips.append("👥 用户行为偏好:")
        tips.append(f"   点赞占比: {avg_like_ratio:.1f}% (用户最主要的互动方式)")
        tips.append(f"   收藏占比: {avg_collect_ratio:.1f}% (内容价值认可)")
        tips.append(f"   评论占比: {avg_comment_ratio:.1f}% (深度参与)")
        
        return tips
    
    def generate_actionable_report(self):
        """生成可执行的分析报告"""
        if self.processed_df is None:
            return "请先加载并预处理数据"
        
        report = []
        report.append("=" * 50)
        report.append("📊 小红书软广投放效果分析报告")
        report.append("=" * 50)
        
        # 核心数据概览
        total_posts = len(self.processed_df)
        total_bloggers = self.processed_df['软文作者'].nunique()
        avg_interaction_rate = self.processed_df['互动率'].mean()
        
        report.append(f"\n📈 数据概览:")
        report.append(f"   总投放内容: {total_posts} 篇")
        report.append(f"   合作博主数: {total_bloggers} 人")
        report.append(f"   平均互动率: {avg_interaction_rate:.2f}%")
        
        # 投放策略建议
        insights = self.get_investment_insights()
        
        report.append(f"\n🎯 投放策略建议:")
        report.append(f"   最佳博主类型: {insights['博主选择策略']['最佳ROI博主类型']}")
        report.append(f"   最佳内容类型: {insights['内容类型策略']['最佳内容类型']}")
        report.append(f"   最佳标题情感: {insights['标题情感策略']['最佳情感类型']}")
        
        # 内容优化建议
        tips = self.get_content_optimization_tips()
        report.append(f"\n💡 内容优化建议:")
        for tip in tips[:10]:  # 只显示前10条
            report.append(f"   {tip}")
        
        return "\n".join(report)

# 使用示例
if __name__ == "__main__":
    analyzer = SmartXHSAnalyzer()
    
    if analyzer.load_data('test.xlsx'):
        analyzer.preprocess_data()
        
        # 生成分析报告
        report = analyzer.generate_actionable_report()
        print(report)
        
        # 获取详细洞察
        insights = analyzer.get_investment_insights()
        
        print("\n" + "=" * 50)
        print("📊 详细数据洞察")
        print("=" * 50)
        
        for category, data in insights.items():
            print(f"\n{category}:")
            if isinstance(data, dict) and '最佳' in list(data.keys())[0]:
                for key, value in data.items():
                    if not isinstance(value, pd.DataFrame):
                        print(f"   {key}: {value}")
