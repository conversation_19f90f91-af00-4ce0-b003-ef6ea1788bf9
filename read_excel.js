const XLSX = require('xlsx');
const fs = require('fs');

// 读取Excel文件
const workbook = XLSX.readFile('./test.xlsx');
const sheetName = workbook.SheetNames[0];
const worksheet = workbook.Sheets[sheetName];

// 转换为JSON
const data = XLSX.utils.sheet_to_json(worksheet);

console.log('=== 数据结构分析 ===');
console.log('总行数:', data.length);
console.log('\n=== 列名 ===');
if (data.length > 0) {
  console.log(Object.keys(data[0]));
}

console.log('\n=== 前3行示例数据 ===');
console.log(JSON.stringify(data.slice(0, 3), null, 2));

console.log('\n=== 数据类型分析 ===');
if (data.length > 0) {
  const sample = data[0];
  Object.keys(sample).forEach(key => {
    const value = sample[key];
    const type = typeof value;
    const isNumber = !isNaN(Number(value));
    console.log(`${key}: ${type} ${isNumber ? '(可转数字)' : ''} - 示例: ${value}`);
  });
}