#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书软广数据分析系统
作者: AI Assistant
功能: 分析小红书软广投放效果，提供数据洞察
"""

import pandas as pd
import numpy as np
import re
from collections import Counter
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class XiaohongshuAnalyzer:
    def __init__(self):
        self.df = None
        self.processed_df = None
        
    def load_data(self, file_path):
        """加载Excel数据"""
        try:
            self.df = pd.read_excel(file_path)
            print(f"✅ 数据加载成功！共 {len(self.df)} 条记录")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def clean_data(self):
        """数据清洗和预处理"""
        if self.df is None:
            print("❌ 请先加载数据")
            return False
            
        self.processed_df = self.df.copy()
        
        # 1. 处理数值字段中的"万"字单位
        self._convert_wan_to_number('粉丝')
        self._convert_wan_to_number('获赞与收藏')
        
        # 2. 处理收藏数字段中的非数字值
        self.processed_df['收藏数'] = self.processed_df['收藏数'].replace('收藏', 0)
        self.processed_df['收藏数'] = pd.to_numeric(self.processed_df['收藏数'], errors='coerce').fillna(0)
        
        # 3. 标准化日期格式
        self._standardize_date()
        
        # 4. 计算衍生指标
        self._calculate_metrics()
        
        # 5. 处理标签数据
        self._process_tags()
        
        print("✅ 数据清洗完成")
        return True
    
    def _convert_wan_to_number(self, column):
        """将包含'万'的数字转换为实际数值"""
        def convert_value(value):
            if pd.isna(value):
                return 0
            value_str = str(value)
            if '万' in value_str:
                number = float(value_str.replace('万', ''))
                return int(number * 10000)
            else:
                try:
                    return int(float(value_str))
                except:
                    return 0
        
        self.processed_df[column] = self.processed_df[column].apply(convert_value)
    
    def _standardize_date(self):
        """标准化日期格式"""
        def parse_date(date_str):
            if pd.isna(date_str):
                return None
            date_str = str(date_str)
            
            # 处理不同的日期格式
            if len(date_str) == 5 and '-' in date_str:  # MM-DD格式
                return f"2024-{date_str}"
            elif len(date_str) == 10:  # YYYY-MM-DD格式
                return date_str
            else:
                return None
        
        self.processed_df['标准化日期'] = self.processed_df['发文日期'].apply(parse_date)
        self.processed_df['发布月份'] = self.processed_df['标准化日期'].apply(
            lambda x: x.split('-')[1] if x else None
        )
    
    def _calculate_metrics(self):
        """计算关键指标"""
        # 互动率 = (点赞+收藏+评论) / 粉丝数
        self.processed_df['总互动数'] = (
            self.processed_df['点赞数'] + 
            self.processed_df['收藏数'] + 
            self.processed_df['评论数']
        )
        
        self.processed_df['互动率'] = np.where(
            self.processed_df['粉丝'] > 0,
            self.processed_df['总互动数'] / self.processed_df['粉丝'] * 100,
            0
        )
        
        # 内容长度
        self.processed_df['标题长度'] = self.processed_df['软文标题'].str.len()
        self.processed_df['内容长度'] = self.processed_df['软文内容'].str.len()
        
        # 博主分层
        self.processed_df['博主层级'] = pd.cut(
            self.processed_df['粉丝'], 
            bins=[0, 1000, 10000, 100000, float('inf')], 
            labels=['尾部博主(<1K)', '腰部博主(1K-1W)', '中部博主(1W-10W)', '头部博主(>10W)']
        )
    
    def _process_tags(self):
        """处理标签数据"""
        def extract_tags(tag_str):
            if pd.isna(tag_str):
                return []
            
            # 移除方括号和引号，按逗号分割
            tag_str = str(tag_str).strip("[]'\"")
            tags = [tag.strip().strip("'\"#") for tag in tag_str.split("',")]
            return [tag for tag in tags if tag and tag != '']
        
        self.processed_df['标签列表'] = self.processed_df['软文标签'].apply(extract_tags)
        
        # 计算标签数量
        self.processed_df['标签数量'] = self.processed_df['标签列表'].apply(len)
    
    def get_basic_stats(self):
        """获取基础统计信息"""
        if self.processed_df is None:
            print("❌ 请先进行数据清洗")
            return None
            
        # 处理时间跨度
        valid_dates = self.processed_df['标准化日期'].dropna()
        time_span = f"{valid_dates.min()} 至 {valid_dates.max()}" if len(valid_dates) > 0 else "无有效日期"

        stats = {
            '数据概览': {
                '总记录数': len(self.processed_df),
                '博主数量': self.processed_df['软文作者'].nunique(),
                '时间跨度': time_span,
                '地域覆盖': self.processed_df['IP属地'].nunique()
            },
            '互动数据': {
                '平均点赞数': self.processed_df['点赞数'].mean(),
                '平均收藏数': self.processed_df['收藏数'].mean(),
                '平均评论数': self.processed_df['评论数'].mean(),
                '平均互动率': f"{self.processed_df['互动率'].mean():.2f}%"
            },
            '内容特征': {
                '平均标题长度': self.processed_df['标题长度'].mean(),
                '平均内容长度': self.processed_df['内容长度'].mean(),
                '平均标签数量': self.processed_df['标签数量'].mean()
            }
        }
        
        return stats
    
    def analyze_blogger_performance(self):
        """博主表现分析"""
        if self.processed_df is None:
            return None
            
        # 按博主分组分析
        blogger_stats = self.processed_df.groupby('软文作者').agg({
            '点赞数': ['mean', 'sum', 'count'],
            '收藏数': ['mean', 'sum'],
            '评论数': ['mean', 'sum'],
            '互动率': 'mean',
            '粉丝': 'first',
            '博主层级': 'first'
        }).round(2)
        
        blogger_stats.columns = ['平均点赞', '总点赞', '发文数', '平均收藏', '总收藏', 
                               '平均评论', '总评论', '平均互动率', '粉丝数', '博主层级']
        
        # 按博主层级分析
        tier_stats = self.processed_df.groupby('博主层级').agg({
            '互动率': 'mean',
            '点赞数': 'mean',
            '收藏数': 'mean',
            '评论数': 'mean',
            '软文作者': 'nunique'
        }).round(2)
        
        return {
            '博主个人表现': blogger_stats.sort_values('平均互动率', ascending=False),
            '博主层级对比': tier_stats
        }
    
    def analyze_content_performance(self):
        """内容表现分析"""
        if self.processed_df is None:
            return None
            
        # 标题长度与互动的关系
        title_analysis = self.processed_df.groupby(
            pd.cut(self.processed_df['标题长度'], bins=5)
        )['互动率'].mean()
        
        # 内容长度与互动的关系  
        content_analysis = self.processed_df.groupby(
            pd.cut(self.processed_df['内容长度'], bins=5)
        )['互动率'].mean()
        
        # 标签数量与互动的关系
        tag_analysis = self.processed_df.groupby('标签数量')['互动率'].mean()
        
        return {
            '标题长度效果': title_analysis,
            '内容长度效果': content_analysis,
            '标签数量效果': tag_analysis
        }
    
    def analyze_tags(self):
        """标签分析"""
        if self.processed_df is None:
            return None
            
        # 提取所有标签
        all_tags = []
        tag_performance = {}
        
        for idx, row in self.processed_df.iterrows():
            tags = row['标签列表']
            interaction_rate = row['互动率']
            
            for tag in tags:
                all_tags.append(tag)
                if tag not in tag_performance:
                    tag_performance[tag] = []
                tag_performance[tag].append(interaction_rate)
        
        # 标签频次统计
        tag_counts = Counter(all_tags)
        
        # 标签效果分析（只分析出现次数>=2的标签）
        tag_effects = {}
        for tag, rates in tag_performance.items():
            if len(rates) >= 2:  # 至少出现2次
                tag_effects[tag] = {
                    '出现次数': len(rates),
                    '平均互动率': np.mean(rates),
                    '互动率标准差': np.std(rates)
                }
        
        return {
            '热门标签TOP10': dict(tag_counts.most_common(10)),
            '高效标签分析': sorted(tag_effects.items(), 
                                key=lambda x: x[1]['平均互动率'], reverse=True)[:10]
        }
    
    def analyze_time_trends(self):
        """时间趋势分析"""
        if self.processed_df is None:
            return None
            
        # 按月份分析
        monthly_stats = self.processed_df.groupby('发布月份').agg({
            '互动率': 'mean',
            '点赞数': 'mean',
            '收藏数': 'mean',
            '评论数': 'mean',
            '软文作者': 'count'
        }).round(2)
        
        monthly_stats.columns = ['平均互动率', '平均点赞', '平均收藏', '平均评论', '发文数量']
        
        return {
            '月度趋势': monthly_stats.sort_index()
        }
    
    def analyze_regional_performance(self):
        """地域表现分析"""
        if self.processed_df is None:
            return None
            
        # IP属地分析
        regional_stats = self.processed_df.groupby('IP属地').agg({
            '互动率': 'mean',
            '点赞数': 'mean', 
            '收藏数': 'mean',
            '评论数': 'mean',
            '软文作者': ['count', 'nunique']
        }).round(2)
        
        regional_stats.columns = ['平均互动率', '平均点赞', '平均收藏', '平均评论', '发文数', '博主数']
        
        return {
            '地域表现': regional_stats.sort_values('平均互动率', ascending=False)
        }
    
    def generate_insights(self):
        """生成数据洞察"""
        if self.processed_df is None:
            return "请先进行数据清洗"
            
        insights = []
        
        # 最佳表现内容
        best_content = self.processed_df.loc[self.processed_df['互动率'].idxmax()]
        insights.append(f"🏆 最高互动率内容: 《{best_content['软文标题']}》(互动率: {best_content['互动率']:.2f}%)")
        
        # 博主层级洞察
        tier_performance = self.processed_df.groupby('博主层级')['互动率'].mean()
        best_tier = tier_performance.idxmax()
        insights.append(f"📊 最佳博主层级: {best_tier} (平均互动率: {tier_performance[best_tier]:.2f}%)")
        
        # 地域洞察
        regional_performance = self.processed_df.groupby('IP属地')['互动率'].mean()
        if len(regional_performance) > 1:
            best_region = regional_performance.idxmax()
            insights.append(f"🌍 最佳投放地域: {best_region} (平均互动率: {regional_performance[best_region]:.2f}%)")
        
        # 内容长度洞察
        optimal_title_length = self.processed_df.groupby(
            pd.cut(self.processed_df['标题长度'], bins=3)
        )['互动率'].mean().idxmax()
        insights.append(f"📝 最佳标题长度区间: {optimal_title_length}")
        
        return insights

if __name__ == "__main__":
    # 使用示例
    analyzer = XiaohongshuAnalyzer()
    
    # 加载数据
    if analyzer.load_data('test.xlsx'):
        # 数据清洗
        analyzer.clean_data()
        
        # 基础统计
        basic_stats = analyzer.get_basic_stats()
        print("\n=== 基础统计 ===")
        for category, stats in basic_stats.items():
            print(f"\n{category}:")
            for key, value in stats.items():
                print(f"  {key}: {value}")
        
        # 生成洞察
        insights = analyzer.generate_insights()
        print("\n=== 关键洞察 ===")
        for insight in insights:
            print(insight)
