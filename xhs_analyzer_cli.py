#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书软广分析系统 - 命令行版本
简单易用，支持Excel文件分析
"""

import pandas as pd
import numpy as np
from collections import Counter
import sys
import os

class XHSAnalyzer:
    def __init__(self):
        self.df = None
        self.processed_df = None
        
    def load_data(self, file_path):
        """加载Excel数据"""
        try:
            self.df = pd.read_excel(file_path)
            print(f"✅ 数据加载成功！共 {len(self.df)} 条记录")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def preprocess_data(self):
        """数据预处理"""
        if self.df is None:
            return False
            
        self.processed_df = self.df.copy()
        
        # 处理数值字段
        self._convert_wan_fields()
        self._calculate_metrics()
        self._classify_content()
        self._categorize_bloggers()
        
        print("✅ 数据预处理完成")
        return True
    
    def _convert_wan_fields(self):
        """处理包含'万'的数值字段"""
        def convert_wan_to_number(value):
            if pd.isna(value):
                return 0
            value_str = str(value)
            if '万' in value_str:
                number = float(value_str.replace('万', ''))
                return int(number * 10000)
            else:
                try:
                    return int(float(value_str))
                except:
                    return 0
        
        self.processed_df['粉丝_数值'] = self.processed_df['粉丝'].apply(convert_wan_to_number)
        self.processed_df['获赞与收藏_数值'] = self.processed_df['获赞与收藏'].apply(convert_wan_to_number)
        
        # 处理收藏数
        self.processed_df['收藏数_数值'] = self.processed_df['收藏数'].replace('收藏', 0)
        self.processed_df['收藏数_数值'] = pd.to_numeric(self.processed_df['收藏数_数值'], errors='coerce').fillna(0)
    
    def _calculate_metrics(self):
        """计算核心指标"""
        # 总互动数
        self.processed_df['总互动数'] = (
            self.processed_df['点赞数'] + 
            self.processed_df['收藏数_数值'] + 
            self.processed_df['评论数']
        )
        
        # 互动率
        self.processed_df['互动率'] = np.where(
            self.processed_df['粉丝_数值'] > 0,
            self.processed_df['总互动数'] / self.processed_df['粉丝_数值'] * 100,
            0
        )
        
        # 用户行为偏好
        self.processed_df['点赞占比'] = np.where(
            self.processed_df['总互动数'] > 0,
            self.processed_df['点赞数'] / self.processed_df['总互动数'] * 100,
            0
        )
        
        self.processed_df['收藏占比'] = np.where(
            self.processed_df['总互动数'] > 0,
            self.processed_df['收藏数_数值'] / self.processed_df['总互动数'] * 100,
            0
        )
        
        self.processed_df['评论占比'] = np.where(
            self.processed_df['总互动数'] > 0,
            self.processed_df['评论数'] / self.processed_df['总互动数'] * 100,
            0
        )
    
    def _classify_content(self):
        """内容分类"""
        def identify_content_type(title, content):
            text = (title + ' ' + content).lower()
            
            if any(word in text for word in ['测评', '避雷', '踩雷', '不推荐', '差评', '不要', '别碰']):
                return '测评避雷'
            elif any(word in text for word in ['推荐', '好吃', '必备', '攻略', '合集', '好用']):
                return '种草推荐'
            elif any(word in text for word in ['程序', '小程序', 'app', '软件', '工具']):
                return '工具介绍'
            elif any(word in text for word in ['总结', '记录', '日常', '每天', '生活']):
                return '生活记录'
            elif any(word in text for word in ['教程', '怎么', '如何', '方法', '步骤']):
                return '教程攻略'
            else:
                return '其他'
        
        self.processed_df['内容类型'] = self.processed_df.apply(
            lambda row: identify_content_type(row['软文标题'], row['软文内容']), axis=1
        )
        
        # 标题情感分析
        def analyze_title_emotion(title):
            positive_words = ['好吃', '推荐', '必备', '好用', '爱了', '绝了', '太好', '完美', '惊喜', '必须']
            negative_words = ['避雷', '不要', '别碰', '差', '坑', '难吃', '不推荐', '踩雷']
            urgent_words = ['限时', '抢', '最后', '马上', '立即', '快', '速度']
            
            has_positive = any(word in title for word in positive_words)
            has_negative = any(word in title for word in negative_words)
            has_urgent = any(word in title for word in urgent_words)
            
            if has_positive and has_urgent:
                return '正面+紧迫'
            elif has_negative and has_urgent:
                return '负面+紧迫'
            elif has_positive:
                return '正面情感'
            elif has_negative:
                return '负面情感'
            elif has_urgent:
                return '紧迫感'
            else:
                return '中性'
        
        self.processed_df['标题情感'] = self.processed_df['软文标题'].apply(analyze_title_emotion)
    
    def _categorize_bloggers(self):
        """博主分类"""
        def categorize_blogger(fans):
            if fans < 1000:
                return '素人博主(<1K)'
            elif fans < 5000:
                return '小博主(1K-5K)'
            elif fans < 20000:
                return '中博主(5K-2W)'
            elif fans < 100000:
                return '大博主(2W-10W)'
            else:
                return '头部博主(>10W)'
        
        self.processed_df['博主类型'] = self.processed_df['粉丝_数值'].apply(categorize_blogger)
    
    def generate_report(self):
        """生成完整分析报告"""
        if self.processed_df is None:
            return "请先加载并预处理数据"
        
        df = self.processed_df
        
        report = []
        report.append("=" * 60)
        report.append("📊 小红书软广投放效果分析报告")
        report.append("=" * 60)
        
        # 1. 数据概览
        report.append(f"\n📈 数据概览:")
        report.append(f"   总投放内容: {len(df)} 篇")
        report.append(f"   合作博主数: {df['软文作者'].nunique()} 人")
        report.append(f"   平均互动率: {df['互动率'].mean():.2f}%")
        report.append(f"   总互动数: {df['总互动数'].sum():,}")
        
        # 2. 博主分析
        report.append(f"\n👥 博主类型分析:")
        blogger_performance = df.groupby('博主类型').agg({
            '互动率': 'mean',
            '软文标题': 'count'
        }).round(2)
        blogger_performance.columns = ['平均互动率', '投放数量']
        blogger_performance = blogger_performance.sort_values('平均互动率', ascending=False)
        
        for blogger_type, stats in blogger_performance.iterrows():
            report.append(f"   {blogger_type}: 平均互动率 {stats['平均互动率']:.1f}%, 投放 {stats['投放数量']} 篇")
        
        best_blogger_type = blogger_performance.index[0]
        report.append(f"\n   🏆 最佳博主类型: {best_blogger_type}")
        
        # 3. 内容类型分析
        report.append(f"\n📝 内容类型分析:")
        content_performance = df.groupby('内容类型').agg({
            '互动率': 'mean',
            '软文标题': 'count'
        }).round(2)
        content_performance.columns = ['平均互动率', '内容数量']
        content_performance = content_performance.sort_values('平均互动率', ascending=False)
        
        for content_type, stats in content_performance.iterrows():
            report.append(f"   {content_type}: 平均互动率 {stats['平均互动率']:.1f}%, 数量 {stats['内容数量']} 篇")
        
        best_content_type = content_performance.index[0]
        report.append(f"\n   🏆 最佳内容类型: {best_content_type}")
        
        # 4. 标题情感分析
        report.append(f"\n💭 标题情感分析:")
        emotion_performance = df.groupby('标题情感').agg({
            '互动率': 'mean',
            '软文标题': 'count'
        }).round(2)
        emotion_performance.columns = ['平均互动率', '内容数量']
        emotion_performance = emotion_performance.sort_values('平均互动率', ascending=False)
        
        for emotion_type, stats in emotion_performance.iterrows():
            report.append(f"   {emotion_type}: 平均互动率 {stats['平均互动率']:.1f}%, 数量 {stats['内容数量']} 篇")
        
        best_emotion_type = emotion_performance.index[0]
        report.append(f"\n   🏆 最佳情感类型: {best_emotion_type}")
        
        # 5. 用户行为偏好
        report.append(f"\n👍 用户行为偏好:")
        avg_like_ratio = df['点赞占比'].mean()
        avg_collect_ratio = df['收藏占比'].mean()
        avg_comment_ratio = df['评论占比'].mean()
        
        report.append(f"   点赞占比: {avg_like_ratio:.1f}% (主要互动方式)")
        report.append(f"   收藏占比: {avg_collect_ratio:.1f}% (内容价值认可)")
        report.append(f"   评论占比: {avg_comment_ratio:.1f}% (深度参与)")
        
        # 6. 高效内容案例
        report.append(f"\n🏆 高效内容案例 (TOP 5):")
        top_content = df.nlargest(5, '互动率')
        
        for idx, (_, row) in enumerate(top_content.iterrows()):
            report.append(f"\n   {idx+1}. 《{row['软文标题']}》")
            report.append(f"      - 互动率: {row['互动率']:.1f}%")
            report.append(f"      - 博主类型: {row['博主类型']}")
            report.append(f"      - 内容类型: {row['内容类型']}")
            report.append(f"      - 标题情感: {row['标题情感']}")
            report.append(f"      - 互动数据: 👍{row['点赞数']} 💾{row['收藏数_数值']} 💬{row['评论数']}")
        
        # 7. 投放建议
        report.append(f"\n🎯 投放策略建议:")
        report.append(f"   1. 优先选择 {best_blogger_type}，性价比最高")
        report.append(f"   2. 重点投放 {best_content_type} 类内容")
        report.append(f"   3. 标题使用 {best_emotion_type} 的表达方式")
        report.append(f"   4. 重点引导点赞({avg_like_ratio:.1f}%)和收藏({avg_collect_ratio:.1f}%)行为")
        report.append(f"   5. 关注评论区互动，提升用户参与度")
        
        # 8. 地域分析（如果有数据）
        if df['IP属地'].notna().sum() > 0:
            report.append(f"\n🌍 地域投放分析:")
            regional_performance = df.groupby('IP属地').agg({
                '互动率': 'mean',
                '软文标题': 'count'
            }).round(2)
            regional_performance.columns = ['平均互动率', '投放数量']
            regional_performance = regional_performance.sort_values('平均互动率', ascending=False)
            
            for region, stats in regional_performance.head(5).iterrows():
                report.append(f"   {region}: 平均互动率 {stats['平均互动率']:.1f}%, 投放 {stats['投放数量']} 篇")
            
            best_region = regional_performance.index[0]
            report.append(f"\n   🏆 最佳投放地域: {best_region}")
        
        report.append(f"\n" + "=" * 60)
        report.append(f"报告生成完成 - {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("=" * 60)
        
        return "\n".join(report)

def main():
    """主函数"""
    print("🎯 小红书软广分析系统")
    print("=" * 40)
    
    # 检查命令行参数
    if len(sys.argv) != 2:
        print("使用方法: python3 xhs_analyzer_cli.py <Excel文件路径>")
        print("示例: python3 xhs_analyzer_cli.py test.xlsx")
        return
    
    file_path = sys.argv[1]
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    # 初始化分析器
    analyzer = XHSAnalyzer()
    
    # 加载和处理数据
    if analyzer.load_data(file_path):
        if analyzer.preprocess_data():
            # 生成报告
            print("\n🔄 正在生成分析报告...")
            report = analyzer.generate_report()
            
            # 显示报告
            print(report)
            
            # 保存报告到文件
            output_file = f"analysis_report_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(f"\n💾 分析报告已保存到: {output_file}")
        else:
            print("❌ 数据预处理失败")
    else:
        print("❌ 数据加载失败")

if __name__ == "__main__":
    main()
