'use client'

import { useState } from 'react'
import { Upload, BarChart3, <PERSON>, Heart, MessageSquare, Download, TrendingUp, Target, Star, BarChart } from 'lucide-react'
import FileUpload from '@/components/FileUpload'
import Dashboard from '@/components/Dashboard'
import ProfessionalAnalytics from '@/components/ProfessionalAnalytics'
import ContentStrategyAnalytics from '@/components/ContentStrategyAnalytics'
import AdvancedAnalytics from '@/components/AdvancedAnalytics'
import CampaignStrategy from '@/components/CampaignStrategy'

export interface NoteData {
  id: string
  title: string
  author: string
  content: string
  date: string
  location: string
  likes: number
  collects: number
  comments: number
  tags: string[]
  userId: string
  ipLocation: string
  following: number
  followers: number
  engagement: number
}

export default function Home() {
  const [data, setData] = useState<NoteData[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  const stats = {
    totalNotes: data.length,
    totalLikes: data.reduce((sum, note) => sum + note.likes, 0),
    totalCollects: data.reduce((sum, note) => sum + note.collects, 0),
    totalComments: data.reduce((sum, note) => sum + note.comments, 0),
    avgEngagement: data.length ? data.reduce((sum, note) => sum + note.engagement, 0) / data.length : 0
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            小红书UGC内容营销分析系统
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            深度分析小红书UGC内容数据，提供专业的营销洞察和可视化报告
          </p>
        </div>

        {/* Stats Overview */}
        {data.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
            <div className="bg-white rounded-xl p-6 shadow-lg border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">总笔记数</p>
                  <p className="text-2xl font-bold text-gray-800">{stats.totalNotes}</p>
                </div>
                <BarChart3 className="w-8 h-8 text-blue-500" />
              </div>
            </div>
            
            <div className="bg-white rounded-xl p-6 shadow-lg border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">总点赞数</p>
                  <p className="text-2xl font-bold text-gray-800">{stats.totalLikes}</p>
                </div>
                <Heart className="w-8 h-8 text-red-500" />
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">总收藏数</p>
                  <p className="text-2xl font-bold text-gray-800">{stats.totalCollects}</p>
                </div>
                <Download className="w-8 h-8 text-green-500" />
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">总评论数</p>
                  <p className="text-2xl font-bold text-gray-800">{stats.totalComments}</p>
                </div>
                <MessageSquare className="w-8 h-8 text-yellow-500" />
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">平均互动</p>
                  <p className="text-2xl font-bold text-gray-800">{Math.round(stats.avgEngagement)}</p>
                </div>
                <TrendingUp className="w-8 h-8 text-purple-500" />
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-lg border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">作者数</p>
                  <p className="text-2xl font-bold text-gray-800">
                    {new Set(data.map(note => note.author)).size}
                  </p>
                </div>
                <Users className="w-8 h-8 text-indigo-500" />
              </div>
            </div>
          </div>
        )}

        {/* Tab Navigation */}
        {data.length > 0 && (
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6">
            {[
              { id: 'overview', label: '数据总览', icon: BarChart3 },
              { id: 'advanced', label: '智能分析', icon: Target },
              { id: 'strategy', label: '投放策略', icon: TrendingUp },
              { id: 'recommend', label: '主播推荐', icon: Star }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id)}
                className={`flex items-center space-x-2 px-6 py-3 rounded-md text-sm font-medium transition-colors ${
                  activeTab === id
                    ? 'bg-white text-gray-800 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{label}</span>
              </button>
            ))}
          </div>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* File Upload Section */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl p-6 shadow-lg border sticky top-8">
              <div className="flex items-center mb-4">
                <Upload className="w-6 h-6 text-blue-500 mr-2" />
                <h2 className="text-xl font-semibold text-gray-800">数据上传</h2>
              </div>
              <FileUpload 
                onDataLoaded={setData}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
              />
              
              {data.length > 0 && (
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    ✅ 已成功加载 {data.length} 条笔记数据
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Content Section */}
          <div className="lg:col-span-3">
            {data.length > 0 ? (
              <>
                {activeTab === 'overview' && <Dashboard data={data} />}
                {activeTab === 'advanced' && <AdvancedAnalytics data={data} />}
                {activeTab === 'strategy' && <CampaignStrategy data={data} />}
                {activeTab === 'recommend' && (
                  <div className="bg-white rounded-xl p-6 shadow-lg border">
                    <div className="flex items-center space-x-2 mb-6">
                      <Star className="w-6 h-6 text-yellow-500" />
                      <h3 className="text-xl font-semibold text-gray-800">专业主播推荐</h3>
                    </div>
                    <ProfessionalAnalytics data={data} />
                  </div>
                )}
              </>
            ) : (
              <div className="bg-white rounded-xl p-12 shadow-lg border text-center">
                <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">
                  等待数据上传
                </h3>
                <p className="text-gray-500">
                  请上传Excel文件开始分析小红书UGC内容数据
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}