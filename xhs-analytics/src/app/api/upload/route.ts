import { NextRequest, NextResponse } from 'next/server'
import * as XLSX from 'xlsx'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }
    
    const buffer = await file.arrayBuffer()
    const workbook = XLSX.read(buffer, { type: 'buffer' })
    const worksheet = workbook.Sheets[workbook.SheetNames[0]]
    const data = XLSX.utils.sheet_to_json(worksheet)
    
    // Process and clean the data
    const processedData = data.map((item: any) => ({
      id: item['笔记链接']?.split('/').pop() || Math.random().toString(36),
      title: item['软文标题'] || '无标题',
      author: item['软文作者'] || '未知作者',
      content: item['软文内容'] || '',
      date: item['发文日期'] || '',
      location: item['发文地点'] || '',
      likes: parseInt(item['点赞数']) || 0,
      collects: parseInt(item['收藏数']) || 0,
      comments: parseInt(item['评论数']) || 0,
      tags: Array.isArray(item['软文标签']) ? item['软文标签'] : [item['软文标签']].filter(Boolean),
      userId: item['小红书号'] || '',
      ipLocation: item['IP属地'] || '',
      following: parseInt(item['关注']) || 0,
      followers: parseInt(item['粉丝']) || 0,
      engagement: parseInt(item['获赞与收藏']) || 0
    }))
    
    return NextResponse.json({ data: processedData, total: processedData.length })
  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: 'Failed to process file' },
      { status: 500 }
    )
  }
}