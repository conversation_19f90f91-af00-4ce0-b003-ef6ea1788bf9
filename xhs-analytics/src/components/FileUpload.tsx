'use client'

import { useState, useRef } from 'react'
import { Upload, FileText, X, Loader } from 'lucide-react'

interface FileUploadProps {
  onDataLoaded: (data: any[]) => void
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
}

export default function FileUpload({ onDataLoaded, isLoading, setIsLoading }: FileUploadProps) {
  const [file, setFile] = useState<File | null>(null)
  const [dragOver, setDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileUpload = async (selectedFile: File) => {
    if (!selectedFile.name.endsWith('.xlsx')) {
      alert('请上传Excel文件 (.xlsx)')
      return
    }

    setFile(selectedFile)
    setIsLoading(true)

    const formData = new FormData()
    formData.append('file', selectedFile)

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        throw new Error('上传失败')
      }

      const result = await response.json()
      onDataLoaded(result.data)
    } catch (error) {
      console.error('Upload error:', error)
      alert('文件处理失败，请检查文件格式')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  const removeFile = () => {
    setFile(null)
    onDataLoaded([])
  }

  return (
    <div className="space-y-4">
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          dragOver 
            ? 'border-blue-400 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".xlsx"
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <div className="space-y-3">
          <Upload className="w-12 h-12 text-gray-400 mx-auto" />
          <div>
            <p className="text-sm font-medium text-gray-600">
              拖放Excel文件或点击上传
            </p>
            <p className="text-xs text-gray-500 mt-1">
              支持 .xlsx 格式文件
            </p>
          </div>
          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? '处理中...' : '选择文件'}
          </button>
        </div>
      </div>

      {file && (
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {isLoading ? (
                <Loader className="w-5 h-5 text-blue-500 animate-spin" />
              ) : (
                <FileText className="w-5 h-5 text-green-500" />
              )}
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-700 truncate">
                  {file.name}
                </p>
                <p className="text-xs text-gray-500">
                  {(file.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            </div>
            <button
              onClick={removeFile}
              disabled={isLoading}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {isLoading && (
        <div className="text-center">
          <Loader className="w-6 h-6 text-blue-500 animate-spin mx-auto" />
          <p className="text-sm text-gray-600 mt-2">正在分析数据...</p>
        </div>
      )}
    </div>
  )
}