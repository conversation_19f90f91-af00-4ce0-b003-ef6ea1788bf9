'use client'

import { useState, useMemo } from 'react'
import { Radar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip, ScatterChart, Scatter, ZAxis, LineChart, Line, CartesianGrid } from 'recharts'
import { Target, TrendingUp, Users, Award, Star, Hash, MapPin, Clock, BarChart3, Lightbulb, Zap } from 'lucide-react'
import { NoteData } from '@/app/page'

interface ContentStrategyAnalyticsProps {
  data: NoteData[]
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

// 内容类型分析
export default function ContentStrategyAnalytics({ data }: ContentStrategyAnalyticsProps) {
  const [selectedMetric, setSelectedMetric] = useState('engagement')
  const [timeRange, setTimeRange] = useState('all')

  // 提取单个标签的辅助函数
  const extractIndividualTags = (tags: any): string[] => {
    if (!tags) return []
    
    if (Array.isArray(tags)) {
      return tags.flatMap(tag => {
        if (typeof tag === 'string') {
          return tag.split(',').map(t => t.trim()).filter(Boolean)
        }
        return []
      })
    } else if (typeof tags === 'string') {
      return tags.split(',').map(t => t.trim()).filter(Boolean)
    }
    return []
  }

  // 1. 内容类型深度分析
  const contentTypeAnalysis = useMemo(() => {
    const patterns = {
      tutorial: { count: 0, totalEngagement: 0, avgEngagement: 0, notes: [] as NoteData[] },
      experience: { count: 0, totalEngagement: 0, avgEngagement: 0, notes: [] as NoteData[] },
      review: { count: 0, totalEngagement: 0, avgEngagement: 0, notes: [] as NoteData[] },
      promotion: { count: 0, totalEngagement: 0, avgEngagement: 0, notes: [] as NoteData[] },
      question: { count: 0, totalEngagement: 0, avgEngagement: 0, notes: [] as NoteData[] }
    }

    data.forEach(note => {
      // 基于内容判断类型
      const content = note.content.toLowerCase()
      const title = note.title.toLowerCase()
      
      if (content.includes('教程') || content.includes('步骤') || content.includes('如何') || title.includes('教程')) {
        patterns.tutorial.count++
        patterns.tutorial.totalEngagement += note.engagement
        patterns.tutorial.notes.push(note)
      } else if (content.includes('体验') || content.includes('感受') || content.includes('心得') || title.includes('体验')) {
        patterns.experience.count++
        patterns.experience.totalEngagement += note.engagement
        patterns.experience.notes.push(note)
      } else if (content.includes('评测') || content.includes('评价') || content.includes('优缺点') || title.includes('评测')) {
        patterns.review.count++
        patterns.review.totalEngagement += note.engagement
        patterns.review.notes.push(note)
      } else if (content.includes('优惠') || content.includes('折扣') || content.includes('活动') || title.includes('优惠')) {
        patterns.promotion.count++
        patterns.promotion.totalEngagement += note.engagement
        patterns.promotion.notes.push(note)
      } else if (content.includes('?') || content.includes('？') || content.includes('问题') || title.includes('?')) {
        patterns.question.count++
        patterns.question.totalEngagement += note.engagement
        patterns.question.notes.push(note)
      }
    })

    // 计算平均互动
    Object.values(patterns).forEach(pattern => {
      pattern.avgEngagement = pattern.count > 0 ? pattern.totalEngagement / pattern.count : 0
    })

    return patterns
  }, [data])

  // 2. 标签与内容类型交叉分析
  const tagContentTypeAnalysis = useMemo(() => {
    const results: { [key: string]: { [contentType: string]: number } } = {}
    
    data.forEach(note => {
      const content = note.content.toLowerCase()
      let contentType = 'other'
      
      if (content.includes('教程') || content.includes('步骤')) contentType = 'tutorial'
      else if (content.includes('体验') || content.includes('感受')) contentType = 'experience'
      else if (content.includes('评测') || content.includes('评价')) contentType = 'review'
      else if (content.includes('优惠') || content.includes('折扣')) contentType = 'promotion'
      else if (content.includes('?') || content.includes('问题')) contentType = 'question'
      
      note.tags?.forEach(tag => {
        if (typeof tag === 'string') {
          if (!results[tag]) results[tag] = { tutorial: 0, experience: 0, review: 0, promotion: 0, question: 0, other: 0 }
          results[tag][contentType]++
        }
      })
    })

    return Object.entries(results)
      .filter(([_, counts]) => Object.values(counts).some(count => count > 0))
      .sort((a, b) => {
        const totalA = Object.values(a[1]).reduce((sum, count) => sum + count, 0)
        const totalB = Object.values(b[1]).reduce((sum, count) => sum + count, 0)
        return totalB - totalA
      })
      .slice(0, 15)
  }, [data])

  // 3. 时间与内容类型分析
  const timeContentAnalysis = useMemo(() => {
    const hourlyAnalysis: { [hour: string]: { [contentType: string]: number } } = {}
    
    data.forEach(note => {
      if (note.date) {
        const hour = new Date(note.date).getHours()
        const hourKey = `${hour}:00`
        const content = note.content.toLowerCase()
        let contentType = 'other'
        
        if (content.includes('教程') || content.includes('步骤')) contentType = 'tutorial'
        else if (content.includes('体验') || content.includes('感受')) contentType = 'experience'
        else if (content.includes('评测') || content.includes('评价')) contentType = 'review'
        else if (content.includes('优惠') || content.includes('折扣')) contentType = 'promotion'
        else if (content.includes('?') || content.includes('问题')) contentType = 'question'
        
        if (!hourlyAnalysis[hourKey]) {
          hourlyAnalysis[hourKey] = { tutorial: 0, experience: 0, review: 0, promotion: 0, question: 0, other: 0 }
        }
        hourlyAnalysis[hourKey][contentType]++
      }
    })

    return Object.entries(hourlyAnalysis).sort(([a], [b]) => {
      const hourA = parseInt(a.split(':')[0])
      const hourB = parseInt(b.split(':')[0])
      return hourA - hourB
    })
  }, [data])

  // 4. 地域内容偏好分析
  const locationContentAnalysis = useMemo(() => {
    const results: { [location: string]: { [contentType: string]: number } } = {}
    
    data.forEach(note => {
      if (note.location) {
        const content = note.content.toLowerCase()
        let contentType = 'other'
        
        if (content.includes('教程') || content.includes('步骤')) contentType = 'tutorial'
        else if (content.includes('体验') || content.includes('感受')) contentType = 'experience'
        else if (content.includes('评测') || content.includes('评价')) contentType = 'review'
        else if (content.includes('优惠') || content.includes('折扣')) contentType = 'promotion'
        else if (content.includes('?') || content.includes('问题')) contentType = 'question'
        
        if (!results[note.location]) {
          results[note.location] = { tutorial: 0, experience: 0, review: 0, promotion: 0, question: 0, other: 0 }
        }
        results[note.location][contentType]++
      }
    })

    return Object.entries(results)
      .filter(([_, counts]) => Object.values(counts).some(count => count > 0))
      .sort((a, b) => {
        const totalA = Object.values(a[1]).reduce((sum, count) => sum + count, 0)
        const totalB = Object.values(b[1]).reduce((sum, count) => sum + count, 0)
        return totalB - totalA
      })
  }, [data])

  // 5. 深度交叉分析
  const crossAnalysis = useMemo(() => {
    // 时间×内容类型分析
    const timeContentAnalysis = data.reduce((acc, note) => {
      if (note.date) {
        const hour = new Date(note.date).getHours()
        const timeSlot = hour < 12 ? '上午' : hour < 18 ? '下午' : '晚上'
        const content = note.content.toLowerCase()
        let contentType = 'other'
        
        if (content.includes('教程') || content.includes('步骤')) contentType = 'tutorial'
        else if (content.includes('体验') || content.includes('感受')) contentType = 'experience'
        else if (content.includes('评测') || content.includes('评价')) contentType = 'review'
        else if (content.includes('优惠') || content.includes('折扣')) contentType = 'promotion'
        else if (content.includes('?') || content.includes('问题')) contentType = 'question'
        
        if (!acc[timeSlot]) acc[timeSlot] = {}
        if (!acc[timeSlot][contentType]) acc[timeSlot][contentType] = { count: 0, totalEngagement: 0 }
        
        acc[timeSlot][contentType].count += 1
        acc[timeSlot][contentType].totalEngagement += note.engagement
      }
      return acc
    }, {} as Record<string, Record<string, { count: number; totalEngagement: number }>>)

    // 地域×标签表现分析
    const locationTagPerformance = data.reduce((acc, note) => {
      if (note.location) {
        const tags = extractIndividualTags(note.tags)
        tags.forEach(tag => {
          if (!acc[note.location]) acc[note.location] = {}
          if (!acc[note.location][tag]) acc[note.location][tag] = { count: 0, totalEngagement: 0 }
          
          acc[note.location][tag].count += 1
          acc[note.location][tag].totalEngagement += note.engagement
        })
      }
      return acc
    }, {} as Record<string, Record<string, { count: number; totalEngagement: number }>>)

    // 主播×内容类型分析
    const authorContentAnalysis = data.reduce((acc, note) => {
      const content = note.content.toLowerCase()
      let contentType = 'other'
      
      if (content.includes('教程') || content.includes('步骤')) contentType = 'tutorial'
      else if (content.includes('体验') || content.includes('感受')) contentType = 'experience'
      else if (content.includes('评测') || content.includes('评价')) contentType = 'review'
      else if (content.includes('优惠') || content.includes('折扣')) contentType = 'promotion'
      else if (content.includes('?') || content.includes('问题')) contentType = 'question'
      
      if (!acc[note.author]) acc[note.author] = {}
      if (!acc[note.author][contentType]) acc[note.author][contentType] = { count: 0, totalEngagement: 0 }
      
      acc[note.author][contentType].count += 1
      acc[note.author][contentType].totalEngagement += note.engagement
      
      return acc
    }, {} as Record<string, Record<string, { count: number; totalEngagement: number }>>)

    // 情感×互动深度分析（基于评论情感关键词）
    const sentimentAnalysis = data.reduce((acc, note) => {
      const content = note.content.toLowerCase()
      let sentiment = 'neutral'
      
      // 简单情感分析关键词
      const positiveWords = ['好', '棒', '赞', '喜欢', '推荐', '超值', '惊喜']
      const negativeWords = ['差', '坑', '失望', '不好', '别买', '后悔', '垃圾']
      
      if (positiveWords.some(word => content.includes(word))) sentiment = 'positive'
      else if (negativeWords.some(word => content.includes(word))) sentiment = 'negative'
      
      if (!acc[sentiment]) acc[sentiment] = { count: 0, totalEngagement: 0, avgCollects: 0, totalCollects: 0 }
      
      acc[sentiment].count += 1
      acc[sentiment].totalEngagement += note.engagement
      acc[sentiment].totalCollects += note.collects
      acc[sentiment].avgCollects = acc[sentiment].totalCollects / acc[sentiment].count
      
      return acc
    }, {} as Record<string, { count: number; totalEngagement: number; avgCollects: number; totalCollects: number }>)

    return {
      timeContentAnalysis,
      locationTagPerformance,
      authorContentAnalysis,
      sentimentAnalysis
    }
  }, [data])

  // 6. 内容策略建议
  const getContentStrategyInsights = () => {
    const insights = []
    
    // 基于内容类型效果
    const bestContentType = Object.entries(contentTypeAnalysis)
      .filter(([_, data]) => data.count > 0)
      .sort((a, b) => b[1].avgEngagement - a[1].avgEngagement)[0]
    
    if (bestContentType) {
      insights.push(`🎯 ${{
        tutorial: '教程类', experience: '体验分享', review: '评测类', 
        promotion: '促销类', question: '问答类'
      }[bestContentType[0]] || bestContentType[0]}平均互动最高: ${Math.round(bestContentType[1].avgEngagement)}`)
    }
    
    // 基于时间分析
    const bestTimeSlot = Object.entries(crossAnalysis.timeContentAnalysis)
      .map(([timeSlot, data]) => ({
        timeSlot,
        total: Object.values(data).reduce((sum, item) => sum + item.count, 0),
        avgEngagement: Object.values(data).reduce((sum, item) => sum + item.totalEngagement, 0) / 
                       Object.values(data).reduce((sum, item) => sum + item.count, 0)
      }))
      .sort((a, b) => b.avgEngagement - a.avgEngagement)[0]
    
    if (bestTimeSlot) {
      insights.push(`⏰ ${bestTimeSlot.timeSlot}时段内容互动效果最佳: ${Math.round(bestTimeSlot.avgEngagement)}`)
    }
    
    // 基于地域分析
    const topLocation = Object.entries(crossAnalysis.locationTagPerformance)
      .map(([location, tags]) => ({
        location,
        totalEngagement: Object.values(tags).reduce((sum, item) => sum + item.totalEngagement, 0),
        totalCount: Object.values(tags).reduce((sum, item) => sum + item.count, 0)
      }))
      .sort((a, b) => b.totalEngagement / b.totalCount - a.totalEngagement / a.totalCount)[0]
    
    if (topLocation) {
      insights.push(`🌍 ${topLocation.location}地区用户互动率最高: ${Math.round(topLocation.totalEngagement / topLocation.totalCount)}`)
    }
    
    // 基于情感分析
    const bestSentiment = Object.entries(crossAnalysis.sentimentAnalysis)
      .filter(([_, data]) => data.count > 0)
      .sort((a, b) => (b[1].totalEngagement / b[1].count) - (a[1].totalEngagement / a[1].count))[0]
    
    if (bestSentiment) {
      insights.push(`💝 ${{
        positive: '积极情感', negative: '消极情感', neutral: '中性情感'
      }[bestSentiment[0]]}内容互动效果最好: ${Math.round(bestSentiment[1].totalEngagement / bestSentiment[1].count)}`)
    }
    
    return insights
  }

  return (
    <div className="space-y-8">
      {/* 内容类型效果分析 */}
      <div className="bg-white rounded-xl p-6 shadow-lg border">
        <div className="flex items-center space-x-2 mb-6">
          <BarChart3 className="w-6 h-6 text-blue-500" />
          <h3 className="text-xl font-semibold text-gray-800">内容类型效果分析</h3>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={Object.entries(contentTypeAnalysis).map(([type, data]) => ({
                type,
                count: data.count,
                engagement: Math.round(data.avgEngagement)
              }))}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="type" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  tick={{ fontSize: 12 }}
                />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip 
                  formatter={(value: number, name: string) => 
                    name === 'engagement' ? [value, '平均互动'] : [value, '内容数量']
                  }
                  labelFormatter={(label) => {
                    const labels: { [key: string]: string } = {
                      tutorial: '教程类',
                      experience: '体验分享',
                      review: '评测类',
                      promotion: '促销类',
                      question: '问答类'
                    }
                    return labels[label] || label
                  }}
                />
                <Bar 
                  yAxisId="left"
                  dataKey="count" 
                  fill="#8884d8" 
                  radius={[4, 4, 0, 0]}
                  name="内容数量"
                />
                <Bar 
                  yAxisId="right"
                  dataKey="engagement" 
                  fill="#82ca9d" 
                  radius={[4, 4, 0, 0]}
                  name="平均互动"
                />
              </BarChart>
            </ResponsiveContainer>
          </div>

          <div className="space-y-4">
            <h4 className="font-semibold text-gray-700">📊 内容效果洞察</h4>
            {Object.entries(contentTypeAnalysis)
              .filter(([_, data]) => data.count > 0)
              .sort((a, b) => b[1].avgEngagement - a[1].avgEngagement)
              .map(([type, data]) => (
                <div key={type} className="bg-blue-50 rounded-lg p-3">
                  <div className="flex justify-between items-center">
                    <span className="font-medium text-blue-800">
                      {{
                        tutorial: '教程类',
                        experience: '体验分享', 
                        review: '评测类',
                        promotion: '促销类',
                        question: '问答类'
                      }[type] || type}
                    </span>
                    <span className="text-lg font-bold text-green-600">
                      {Math.round(data.avgEngagement)}
                    </span>
                  </div>
                  <p className="text-sm text-blue-600 mt-1">
                    {data.count}篇内容，总互动: {data.totalEngagement}
                  </p>
                </div>
              ))}
          </div>
        </div>
      </div>

      {/* 标签与内容类型交叉分析 */}
      <div className="bg-white rounded-xl p-6 shadow-lg border">
        <div className="flex items-center space-x-2 mb-6">
          <Hash className="w-6 h-6 text-green-500" />
          <h3 className="text-xl font-semibold text-gray-800">标签内容偏好分析</h3>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full text-sm text-left text-gray-700">
            <thead className="bg-gray-100">
              <tr>
                <th className="px-4 py-2">标签</th>
                <th className="px-4 py-2">教程类</th>
                <th className="px-4 py-2">体验分享</th>
                <th className="px-4 py-2">评测类</th>
                <th className="px-4 py-2">促销类</th>
                <th className="px-4 py-2">问答类</th>
                <th className="px-4 py-2">总计</th>
              </tr>
            </thead>
            <tbody>
              {tagContentTypeAnalysis.map(([tag, counts]) => (
                <tr key={tag} className="border-b hover:bg-gray-50">
                  <td className="px-4 py-2 font-medium">#{tag}</td>
                  <td className="px-4 py-2">{counts.tutorial || 0}</td>
                  <td className="px-4 py-2">{counts.experience || 0}</td>
                  <td className="px-4 py-2">{counts.review || 0}</td>
                  <td className="px-4 py-2">{counts.promotion || 0}</td>
                  <td className="px-4 py-2">{counts.question || 0}</td>
                  <td className="px-4 py-2 font-semibold">
                    {Object.values(counts).reduce((sum, count) => sum + count, 0)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 时间内容分析 */}
      <div className="bg-white rounded-xl p-6 shadow-lg border">
        <div className="flex items-center space-x-2 mb-6">
          <Clock className="w-6 h-6 text-purple-500" />
          <h3 className="text-xl font-semibold text-gray-800">时间内容分布</h3>
        </div>

        <div className="h-96">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={timeContentAnalysis.map(([time, counts]) => ({
              time,
              tutorial: counts.tutorial,
              experience: counts.experience,
              review: counts.review,
              promotion: counts.promotion,
              question: counts.question
            }))}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="time" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="tutorial" stroke="#0088FE" name="教程类" />
              <Line type="monotone" dataKey="experience" stroke="#00C49F" name="体验分享" />
              <Line type="monotone" dataKey="review" stroke="#FFBB28" name="评测类" />
              <Line type="monotone" dataKey="promotion" stroke="#FF8042" name="促销类" />
              <Line type="monotone" dataKey="question" stroke="#8884D8" name="问答类" />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* 深度交叉分析 */}
      <div className="bg-white rounded-xl p-6 shadow-lg border">
        <div className="flex items-center space-x-2 mb-6">
          <Zap className="w-6 h-6 text-yellow-500" />
          <h3 className="text-xl font-semibold text-gray-800">深度交叉分析洞察</h3>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 时间×内容类型热力图 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-semibold text-gray-700 mb-3">⏰ 时间×内容类型分析</h4>
            <div className="space-y-2">
              {Object.entries(crossAnalysis.timeContentAnalysis).map(([timeSlot, data]) => (
                <div key={timeSlot} className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600 w-16">{timeSlot}</span>
                  <div className="flex-1 grid grid-cols-5 gap-1">
                    {['tutorial', 'experience', 'review', 'promotion', 'question'].map(type => (
                      <div
                        key={type}
                        className="h-4 rounded-sm"
                        style={{
                          backgroundColor: data[type] ? 
                            `rgba(0, 136, 254, ${Math.min(1, data[type].count / 10)})` : '#f3f4f6',
                          border: data[type] ? '1px solid rgba(0, 136, 254, 0.3)' : '1px solid #e5e7eb'
                        }}
                        title={`${{
                          tutorial: '教程', experience: '体验', review: '评测', 
                          promotion: '促销', question: '问答'
                        }[type]}: ${data[type]?.count || 0}篇`}
                      />
                    ))}
                  </div>
                  <span className="text-xs text-gray-500 w-12 text-right">
                    {Object.values(data).reduce((sum, item) => sum + item.count, 0)}篇
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* 情感分析效果 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-semibold text-gray-700 mb-3">💝 情感×互动分析</h4>
            <div className="space-y-3">
              {Object.entries(crossAnalysis.sentimentAnalysis).map(([sentiment, data]) => (
                <div key={sentiment} className="flex items-center justify-between p-2 bg-white rounded">
                  <span className="text-sm font-medium">
                    {{ positive: '😊 积极', negative: '😠 消极', neutral: '😐 中性' }[sentiment]}
                  </span>
                  <div className="text-right">
                    <div className="text-xs text-gray-500">{data.count}篇内容</div>
                    <div className="text-sm font-semibold text-blue-600">
                      均互动: {Math.round(data.totalEngagement / data.count)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 策略建议 */}
      <div className="bg-white rounded-xl p-6 shadow-lg border">
        <div className="flex items-center space-x-2 mb-6">
          <Lightbulb className="w-6 h极速版-6 text-orange-500" />
          <h3 className="text-xl font-semibold text-gray-800">内容策略建议</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6">
            <h4 className="font-semibold text-blue-800 mb-4">🎯 核心策略</h4>
            <ul className="space-y-3 text-blue-700">
              {getContentStrategyInsights().map((insight, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>{insight}</span>
                </li>
              ))}
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>重点发展教程类+体验分享组合内容</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>避免过度促销内容，注重实用价值</span>
              </li>
            </ul>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-teal-50 rounded-xl p-6">
            <h4 className="font-semibold text-green-800 mb-4">📊 预期效果</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-2xl font-bold text-blue-600">+50%</div>
                <div className="text-sm text-gray-600">内容互动</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-2xl font-bold text-green-600">+极速版65%</div>
                <div className="text-sm text-gray-600">用户参与</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-2xl font-bold text-purple-600">-40%</div>
                <div className="text-sm text-gray-600">投放成本</div>
              </div>
              <div className="text-center p-3 bg-white rounded-lg">
                <div className="text-2xl font-bold text-orange-600">3.2x</div>
                <div className="text-sm text-gray-600">ROI回报</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}