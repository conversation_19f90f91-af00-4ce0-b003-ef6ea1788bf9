'use client'

import { useState, useMemo } from 'react'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line, Area, AreaChart } from 'recharts'
import { Target, DollarSign, Clock, Users, TrendingUp, AlertCircle, CheckCircle, Lightbulb, Calendar, MapPin } from 'lucide-react'
import { NoteData } from '@/app/page'

interface CampaignStrategyProps {
  data: NoteData[]
}

// 投放策略建议
interface CampaignRecommendation {
  strategy: string
  budget: number
  expectedReach: number
  expectedEngagement: number
  roi: number
  timeline: string
  kpis: string[]
}

// 竞争分析
interface CompetitiveAnalysis {
  category: string
  saturation: 'low' | 'medium' | 'high'
  opportunity: number
  avgCost: number
  successRate: number
}

export default function CampaignStrategy({ data }: CampaignStrategyProps) {
  const [selectedBudget, setSelectedBudget] = useState(50000)
  const [selectedGoal, setSelectedGoal] = useState('awareness')

  // 地域分析
  const locationAnalysis = useMemo(() => {
    const locationStats = data.reduce((acc, note) => {
      const location = note.ipLocation || note.location || '未知'
      if (!acc[location]) {
        acc[location] = {
          posts: 0,
          totalEngagement: 0,
          avgEngagement: 0,
          users: new Set()
        }
      }
      acc[location].posts++
      acc[location].totalEngagement += note.engagement
      acc[location].users.add(note.userId)
      return acc
    }, {} as Record<string, any>)

    return Object.entries(locationStats)
      .map(([location, stats]) => ({
        location,
        posts: stats.posts,
        avgEngagement: Math.round(stats.totalEngagement / stats.posts),
        users: stats.users.size,
        marketPenetration: (stats.users.size / data.length) * 100
      }))
      .sort((a, b) => b.avgEngagement - a.avgEngagement)
      .slice(0, 10)
  }, [data])

  // 竞争环境分析
  const competitiveAnalysis = useMemo(() => {
    const categories = ['美妆', '时尚', '生活', '美食', '旅行', '科技']
    
    return categories.map(category => {
      const categoryPosts = data.filter(note => 
        note.tags?.some(tag => 
          typeof tag === 'string' && tag.includes(category)
        )
      )
      
      const saturation = categoryPosts.length > 20 ? 'high' : 
                       categoryPosts.length > 10 ? 'medium' : 'low'
      
      const avgEngagement = categoryPosts.length > 0 
        ? categoryPosts.reduce((sum, note) => sum + note.engagement, 0) / categoryPosts.length
        : 0
      
      return {
        category,
        posts: categoryPosts.length,
        saturation,
        opportunity: saturation === 'low' ? 85 : saturation === 'medium' ? 60 : 35,
        avgCost: saturation === 'high' ? 150 : saturation === 'medium' ? 100 : 80,
        successRate: avgEngagement > 1000 ? 80 : avgEngagement > 500 ? 60 : 40,
        avgEngagement: Math.round(avgEngagement)
      }
    })
  }, [data])

  // 投放策略生成
  const campaignRecommendations = useMemo(() => {
    const strategies: CampaignRecommendation[] = []
    
    // 基于预算和目标生成策略
    if (selectedGoal === 'awareness') {
      strategies.push({
        strategy: '品牌认知提升策略',
        budget: selectedBudget * 0.6,
        expectedReach: selectedBudget * 20,
        expectedEngagement: selectedBudget * 5,
        roi: 3.2,
        timeline: '4-6周',
        kpis: ['曝光量', '品牌提及', '用户认知度', '内容互动率']
      })
      
      strategies.push({
        strategy: '腰部博主矩阵策略',
        budget: selectedBudget * 0.4,
        expectedReach: selectedBudget * 15,
        expectedEngagement: selectedBudget * 8,
        roi: 4.1,
        timeline: '3-4周',
        kpis: ['互动率', '内容质量', '粉丝增长', '话题讨论度']
      })
    } else if (selectedGoal === 'conversion') {
      strategies.push({
        strategy: '转化导向策略',
        budget: selectedBudget * 0.7,
        expectedReach: selectedBudget * 12,
        expectedEngagement: selectedBudget * 10,
        roi: 5.8,
        timeline: '2-3周',
        kpis: ['转化率', '客单价', '复购率', 'ROI']
      })
      
      strategies.push({
        strategy: '精准人群策略',
        budget: selectedBudget * 0.3,
        expectedReach: selectedBudget * 8,
        expectedEngagement: selectedBudget * 12,
        roi: 6.2,
        timeline: '1-2周',
        kpis: ['精准触达', '转化成本', '用户质量', '留存率']
      })
    }
    
    return strategies
  }, [selectedBudget, selectedGoal])

  // 最佳投放时间分析
  const optimalTiming = useMemo(() => {
    const hourlyData = Array.from({ length: 24 }, (_, hour) => {
      const hourPosts = data.filter(note => new Date(note.date).getHours() === hour)
      const avgEngagement = hourPosts.length > 0 
        ? hourPosts.reduce((sum, note) => sum + note.engagement, 0) / hourPosts.length 
        : 0
      
      return {
        hour,
        posts: hourPosts.length,
        avgEngagement: Math.round(avgEngagement),
        recommendation: avgEngagement > 1200 ? 'optimal' : 
                       avgEngagement > 800 ? 'good' : 
                       avgEngagement > 400 ? 'fair' : 'poor'
      }
    })

    const weeklyData = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'].map((day, index) => {
      const dayPosts = data.filter(note => new Date(note.date).getDay() === index)
      const avgEngagement = dayPosts.length > 0 
        ? dayPosts.reduce((sum, note) => sum + note.engagement, 0) / dayPosts.length 
        : 0
      
      return {
        day,
        posts: dayPosts.length,
        avgEngagement: Math.round(avgEngagement)
      }
    })

    return { hourlyData, weeklyData }
  }, [data])

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4']

  return (
    <div className="space-y-6">
      {/* 策略配置 */}
      <div className="bg-white rounded-xl p-6 shadow-lg border">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">投放策略配置</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              投放预算 (元)
            </label>
            <select 
              value={selectedBudget}
              onChange={(e) => setSelectedBudget(Number(e.target.value))}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value={10000}>1万 - 小规模测试</option>
              <option value={30000}>3万 - 中等规模</option>
              <option value={50000}>5万 - 标准投放</option>
              <option value={100000}>10万 - 大规模投放</option>
              <option value={200000}>20万 - 品牌级投放</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              投放目标
            </label>
            <select 
              value={selectedGoal}
              onChange={(e) => setSelectedGoal(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="awareness">品牌认知</option>
              <option value="conversion">转化导向</option>
              <option value="engagement">互动提升</option>
            </select>
          </div>
        </div>
      </div>

      {/* 推荐策略 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {campaignRecommendations.map((strategy, index) => (
          <div key={index} className="bg-white rounded-xl p-6 shadow-lg border">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h4 className="text-lg font-semibold text-gray-800">{strategy.strategy}</h4>
                <p className="text-sm text-gray-600 mt-1">预算: ¥{strategy.budget.toLocaleString()}</p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-green-600">{strategy.roi}x</div>
                <div className="text-xs text-gray-500">预期ROI</div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-lg font-semibold text-blue-600">
                  {(strategy.expectedReach / 1000).toFixed(0)}K
                </div>
                <div className="text-xs text-gray-600">预期触达</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-lg font-semibold text-green-600">
                  {(strategy.expectedEngagement / 1000).toFixed(0)}K
                </div>
                <div className="text-xs text-gray-600">预期互动</div>
              </div>
            </div>
            
            <div className="mb-4">
              <div className="flex items-center text-sm text-gray-600 mb-2">
                <Clock className="w-4 h-4 mr-2" />
                执行周期: {strategy.timeline}
              </div>
            </div>
            
            <div>
              <div className="text-sm font-medium text-gray-700 mb-2">关键指标 (KPIs)</div>
              <div className="flex flex-wrap gap-2">
                {strategy.kpis.map((kpi, kpiIndex) => (
                  <span 
                    key={kpiIndex}
                    className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                  >
                    {kpi}
                  </span>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 竞争环境分析 */}
      <div className="bg-white rounded-xl p-6 shadow-lg border">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">竞争环境分析</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left p-3">品类</th>
                <th className="text-left p-3">市场饱和度</th>
                <th className="text-left p-3">机会指数</th>
                <th className="text-left p-3">平均成本</th>
                <th className="text-left p-3">成功率</th>
                <th className="text-left p-3">建议</th>
              </tr>
            </thead>
            <tbody>
              {competitiveAnalysis.map((item, index) => (
                <tr key={index} className="border-b hover:bg-gray-50">
                  <td className="p-3 font-medium">{item.category}</td>
                  <td className="p-3">
                    <span className={`px-2 py-1 rounded text-xs ${
                      item.saturation === 'low' ? 'bg-green-100 text-green-800' :
                      item.saturation === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {item.saturation === 'low' ? '低' : 
                       item.saturation === 'medium' ? '中' : '高'}
                    </span>
                  </td>
                  <td className="p-3">
                    <div className="flex items-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full" 
                          style={{ width: `${item.opportunity}%` }}
                        />
                      </div>
                      <span className="text-xs">{item.opportunity}</span>
                    </div>
                  </td>
                  <td className="p-3">¥{item.avgCost}/CPM</td>
                  <td className="p-3">{item.successRate}%</td>
                  <td className="p-3">
                    {item.opportunity > 70 ? (
                      <span className="flex items-center text-green-600">
                        <CheckCircle className="w-4 h-4 mr-1" />
                        推荐
                      </span>
                    ) : item.opportunity > 50 ? (
                      <span className="flex items-center text-yellow-600">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        谨慎
                      </span>
                    ) : (
                      <span className="flex items-center text-red-600">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        避免
                      </span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 投放时机优化 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-lg border">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">最佳投放时间</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={optimalTiming.hourlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip />
                <Area 
                  type="monotone" 
                  dataKey="avgEngagement" 
                  stroke="#3B82F6" 
                  fill="#3B82F6" 
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 text-sm text-gray-600">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-2" />
                <span>最佳时段 (19-22点)</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2" />
                <span>次佳时段 (12-14点)</span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-lg border">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">地域投放建议</h3>
          <div className="space-y-3">
            {locationAnalysis.slice(0, 6).map((location, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <MapPin className="w-4 h-4 text-gray-500 mr-2" />
                  <span className="font-medium">{location.location}</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-blue-600">
                    {location.avgEngagement.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-500">
                    {location.users} 用户 · {location.posts} 篇
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 执行建议 */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
        <div className="flex items-start space-x-3">
          <Lightbulb className="w-6 h-6 text-blue-600 mt-1" />
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">执行建议</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
              <div>
                <h4 className="font-medium mb-2">投放策略</h4>
                <ul className="space-y-1">
                  <li>• 优先选择机会指数高的品类</li>
                  <li>• 采用70%腰部+30%头部的博主组合</li>
                  <li>• 重点关注19-22点黄金时段</li>
                  <li>• 周末投放效果通常更佳</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">风险控制</h4>
                <ul className="space-y-1">
                  <li>• 分批投放，及时调整策略</li>
                  <li>• 设置明确的KPI阈值</li>
                  <li>• 建立实时监控机制</li>
                  <li>• 准备备选博主名单</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}