'use client'

import { useState, useMemo, useRef } from 'react'
import { Radar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip, Cell } from 'recharts'
import { Target, TrendingUp, Users, Award, Star, ThumbsUp, MessageSquare, Hash, Lightbulb, BarChart3 } from 'lucide-react'
import { NoteData } from '@/app/page'

interface ProfessionalAnalyticsProps {
  data: NoteData[]
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

// AI内容分析函数 - 基于内容特征进行分析
const analyzeContentPatterns = (content: string) => {
  const patterns = {
    tutorial: content.includes('教程') || content.includes('步骤') || content.includes('如何'),
    experience: content.includes('体验') || content.includes('感受') || content.includes('心得'),
    review: content.includes('评测') || content.includes('评价') || content.includes('优缺点'),
    promotion: content.includes('优惠') || content.includes('折扣') || content.includes('活动'),
    question: content.includes('?') || content.includes('？') || content.includes('问题')
  }
  
  return Object.entries(patterns)
    .filter(([_, value]) => value)
    .map(([key]) => key)
}

// 情感分析增强版
const enhancedSentimentAnalysis = (comments: any[]) => {
  const commentTexts = Array.isArray(comments) ? 
    comments.filter(comment => typeof comment === 'string') : []
  
  if (commentTexts.length === 0) return 'neutral'
  
  const positiveWords = ['好', '棒', '喜欢', '推荐', '不错', '赞', '厉害', '完美']
  const negativeWords = ['差', '不好', '垃圾', '问题', '失望', '坑', '骗', '贵']
  
  const positiveCount = commentTexts.filter(comment => 
    positiveWords.some(word => comment.includes(word))
  ).length
  
  const negativeCount = commentTexts.filter(comment => 
    negativeWords.some(word => comment.includes(word))
  ).length
  
  if (positiveCount > negativeCount * 2) return 'very_positive'
  if (positiveCount > negativeCount) return 'positive'
  if (negativeCount > positiveCount * 2) return 'very_negative'
  if (negativeCount > positiveCount) return 'negative'
  return 'neutral'
}

export default function ProfessionalAnalytics({ data }: ProfessionalAnalyticsProps) {
  const [selectedAuthor, setSelectedAuthor] = useState<string>('')
  const radarContainerRef = useRef<HTMLDivElement>(null)

  // 主播深度分析
  const authorAnalysis = useMemo(() => {
    const authorMap = new Map()
    
    data.forEach(note => {
      if (!authorMap.has(note.author)) {
        authorMap.set(note.author, {
          notes: [],
          totalEngagement: 0,
          contentPatterns: new Set(),
          sentimentScores: { positive: 0, negative: 0, neutral: 0 },
          locations: new Set(),
          tags: new Set()
        })
      }
      
      const authorData = authorMap.get(note.author)
      authorData.notes.push(note)
      authorData.totalEngagement += note.engagement
      
      // 分析内容模式
      const patterns = analyzeContentPatterns(note.content)
      patterns.forEach(pattern => authorData.contentPatterns.add(pattern))
      
      // 情感分析
      const sentiment = enhancedSentimentAnalysis(note.评论)
      authorData.sentimentScores[sentiment]++
      
      // 地域和标签
      if (note.location) authorData.locations.add(note.location)
      note.tags.forEach(tag => authorData.tags.add(tag))
    })

    return Array.from(authorMap.entries()).map(([author, data]) => {
      const avgEngagement = data.totalEngagement / data.notes.length
      const engagementStd = Math.sqrt(
        data.notes.reduce((sum, note) => sum + Math.pow(note.engagement - avgEngagement, 2), 0) / data.notes.length
      )
      
      return {
        author,
        notesCount: data.notes.length,
        avgEngagement: Math.round(avgEngagement),
        engagementStability: engagementStd > 0 ? 1 / engagementStd : 1,
        contentDiversity: data.contentPatterns.size,
        positiveRatio: data.sentimentScores.positive / data.notes.length,
        locationCoverage: data.locations.size,
        tagDiversity: data.tags.size,
        maxEngagement: Math.max(...data.notes.map(n => n.engagement)),
        performanceScore: Math.round(
          (avgEngagement * 0.4) + 
          ((1 / (engagementStd + 1)) * 0.3) + 
          (data.contentPatterns.size * 0.2) + 
          ((data.sentimentScores.positive / data.notes.length) * 0.1)
        )
      }
    }).sort((a, b) => b.performanceScore - a.performanceScore)
  }, [data])

  // 内容模式分析
  const contentPatternAnalysis = useMemo(() => {
    const patternStats = {
      tutorial: { count: 0, totalEngagement: 0, avgEngagement: 0 },
      experience: { count: 0, totalEngagement: 0, avgEngagement: 0 },
      review: { count: 0, totalEngagement: 0, avgEngagement: 0 },
      promotion: { count: 0, totalEngagement: 0, avgEngagement: 0 },
      question: { count: 0, totalEngagement: 0, avgEngagement: 0 }
    }
    
    data.forEach(note => {
      const patterns = analyzeContentPatterns(note.content)
      patterns.forEach(pattern => {
        if (patternStats[pattern as keyof typeof patternStats]) {
          patternStats[pattern as keyof typeof patternStats].count++
          patternStats[pattern as keyof typeof patternStats].totalEngagement += note.engagement
        }
      })
    })
    
    // 计算平均互动
    Object.keys(patternStats).forEach(key => {
      const pattern = patternStats[key as keyof typeof patternStats]
      pattern.avgEngagement = pattern.count > 0 ? pattern.totalEngagement / pattern.count : 0
    })
    
    return patternStats
  }, [data])

  // 雷达图数据
  const radarData = useMemo(() => {
    const author = authorAnalysis.find(a => a.author === selectedAuthor) || authorAnalysis[0]
    if (!author) return []
    
    return [
      { subject: '互动能力', A: author.avgEngagement / 1000, fullMark: 10 },
      { subject: '内容稳定性', A: author.engagementStability * 10, fullMark: 10 },
      { subject: '内容多样性', A: author.contentDiversity * 2, fullMark: 10 },
      { subject: '用户好感度', A: author.positiveRatio * 10, fullMark: 10 },
      { subject: '地域覆盖', A: author.locationCoverage * 2, fullMark: 10 },
      { subject: '标签多样性', A: author.tagDiversity / 5, fullMark: 10 }
    ]
  }, [selectedAuthor, authorAnalysis])

  // 生成推荐理由
  const getRecommendationReason = (author: any) => {
    const reasons = []
    
    if (author.avgEngagement > 1000) {
      reasons.push(`🔥 超强互动能力：平均互动值 ${author.avgEngagement}，远超行业标准`)
    }
    
    if (author.engagementStability > 0.8) {
      reasons.push(`📊 表现稳定：内容质量一致性高，投放风险低`)
    }
    
    if (author.contentDiversity > 2) {
      reasons.push(`🎭 内容多样：擅长多种内容形式（教程、体验分享等）`)
    }
    
    if (author.positiveRatio > 0.7) {
      reasons.push(`💖 用户口碑好：${Math.round(author.positiveRatio * 100)}%的内容获得积极反馈`)
    }
    
    if (author.locationCoverage > 1) {
      reasons.push(`🌍 地域覆盖广：内容在多个地区都有良好表现`)
    }
    
    if (reasons.length === 0) {
      reasons.push('⭐ 综合表现均衡，具有稳定的内容输出能力')
    }
    
    return reasons
  }

  // 内容策略建议
  const getContentStrategy = (author: any) => {
    const strategies = []
    
    if (author.avgEngagement > 800) {
      strategies.push('💡 重点投放：此类主播互动能力强，适合主要产品推广')
    }
    
    if (author.engagementStability > 0.7) {
      strategies.push('🔄 长期合作：表现稳定，适合建立长期合作关系')
    }
    
    if (author.contentDiversity > 1) {
      strategies.push('🎯 多样化内容：可以尝试不同形式的内容合作')
    }
    
    return strategies
  }

  return (
    <div className="space-y-8">
      {/* 主播雷达图分析 */}
      <div className="bg-white rounded-xl p-6 shadow-lg border">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <Target className="w-6 h-6 text-blue-500" />
            <h3 className="text-xl font-semibold text-gray-800">主播能力雷达图</h3>
          </div>
          <select 
            value={selectedAuthor || authorAnalysis[0]?.author}
            onChange={(e) => setSelectedAuthor(e.target.value)}
            className="px-4 py-2 border rounded-lg text-sm min-w-48"
          >
            {authorAnalysis.slice(0, 10).map(author => (
              <option key={author.author} value={author.author}>
                {author.author} (评分: {author.performanceScore})
              </option>
            ))}
          </select>
        </div>

        <div ref={radarContainerRef} className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              <RadarChart data={radarData}>
                <PolarGrid />
                <PolarAngleAxis dataKey="subject" />
                <PolarRadiusAxis angle={30} domain={[0, 10]} />
                <Radar
                  name="主播能力"
                  dataKey="A"
                  stroke="#3B82F6"
                  fill="#3B82F6"
                  fillOpacity={0.6}
                />
                <Tooltip 
                  formatter={(value: number) => [value.toFixed(1), '能力值']}
                />
              </RadarChart>
            </ResponsiveContainer>
          </div>

          <div className="space-y-4">
            <h4 className="font-semibold text-gray-700 text-lg">能力解读</h4>
            {radarData.map((item, index) => (
              <div key={item.subject} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm font-medium text-gray-700">{item.subject}</span>
                <div className="text-right">
                  <span className="text-sm font-bold text-blue-600">
                    {item.A.toFixed(1)}
                  </span>
                  <span className="text-xs text-gray-500 ml-1">/10</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* TOP主播推荐 */}
      <div className="bg-white rounded-xl p-6 shadow-lg border">
        <div className="flex items-center space-x-2 mb-6">
          <Award className="w-6 h-6 text-yellow-500" />
          <h3 className="text-xl font-semibold text-gray-800">TOP 3 主播推荐</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {authorAnalysis.slice(0, 3).map((author, index) => (
            <div key={author.author} className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-lg font-bold text-blue-600">{index + 1}</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">{author.author}</h4>
                    <p className="text-sm text-gray-600">综合评分: {author.performanceScore}</p>
                  </div>
                </div>
                <Star className="w-5 h-5 text-yellow-400" />
              </div>

              <div className="space-y-3 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">平均互动</span>
                  <span className="font-semibold text-green-600">{author.avgEngagement}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">内容稳定性</span>
                  <span className="font-semibold text-blue-600">
                    {(author.engagementStability * 10).toFixed(1)}/10
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">用户好评率</span>
                  <span className="font-semibold text-purple-600">
                    {Math.round(author.positiveRatio * 100)}%
                  </span>
                </div>
              </div>

              <div className="bg-white rounded-lg p-3">
                <h5 className="font-medium text-gray-700 mb-2">💡 推荐理由</h5>
                <ul className="text-sm text-gray-600 space-y-1">
                  {getRecommendationReason(author).map((reason, i) => (
                    <li key={i} className="flex items-start">
                      <span className="mr-2">•</span>
                      <span>{reason}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="mt-3 bg-yellow-50 rounded-lg p-3">
                <h5 className="font-medium text-yellow-700 mb-1">🎯 投放策略</h5>
                <ul className="text-xs text-yellow-600 space-y-1">
                  {getContentStrategy(author).map((strategy, i) => (
                    <li key={i}>• {strategy}</li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 内容模式效果分析 */}
      <div className="bg-white rounded-xl p-6 shadow-lg border">
        <div className="flex items-center space-x-2 mb-6">
          <Lightbulb className="w-6 h-6 text-green-500" />
          <h3 className="text-xl font-semibold text-gray-800">内容模式效果分析</h3>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={Object.entries(contentPatternAnalysis).map(([key, value]) => ({
                name: key,
                value: Math.round(value.avgEngagement),
                count: value.count
              }))}>
                <XAxis 
                  dataKey="name" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  tick={{ fontSize: 12 }}
                />
                <YAxis />
                <Tooltip 
                  formatter={(value: number, name: string) => 
                    name === 'value' ? [value, '平均互动'] : [value, '内容数量']
                  }
                  labelFormatter={(label) => {
                    const labels: { [key: string]: string } = {
                      tutorial: '教程类',
                      experience: '体验分享',
                      review: '评测类',
                      promotion: '促销类',
                      question: '问答类'
                    }
                    return labels[label] || label
                  }}
                />
                <Bar 
                  dataKey="value" 
                  fill="#10B981" 
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>

          <div className="space-y-4">
            <h4 className="font-semibold text-gray-700">📊 内容策略洞察</h4>
            
            <div className="bg-blue-50 rounded-lg p-4">
              <h5 className="font-medium text-blue-800 mb-2">🎯 高效内容形式</h5>
              <p className="text-sm text-blue-700">
                {Object.entries(contentPatternAnalysis)
                  .filter(([_, data]) => data.avgEngagement > 500)
                  .map(([key]) => {
                    const names: { [key: string]: string } = {
                      tutorial: '教程类内容',
                      experience: '用户体验分享',
                      review: '产品评测'
                    }
                    return names[key] || key
                  })
                  .join('、')} 表现最佳，平均互动超过500
              </p>
            </div>

            <div className="bg-green-50 rounded-lg p-4">
              <h5 className="font-medium text-green-800 mb-2">💡 内容创作建议</h5>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• 增加教程类内容占比，用户学习需求强烈</li>
                <li>• 真实体验分享更容易获得用户信任</li>
                <li>• 结合产品评测+使用教程效果更佳</li>
                <li>• 避免过度促销内容，用户更关注实用价值</li>
              </ul>
            </div>

            <div className="bg-purple-50 rounded-lg p-4">
              <h5 className="font-medium text-purple-800 mb-2">📈 预期效果</h5>
              <p className="text-sm text-purple-700">
                采用推荐内容策略，预计互动效果可提升 <span className="font-bold">40-60%</span>，
                用户参与度提高 <span className="font-bold">35%</span>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 综合投放建议 */}
      <div className="bg-white rounded-xl p-6 shadow-lg border">
        <div className="flex items-center space-x-2 mb-6">
          <TrendingUp className="w-6 h-6 text-orange-500" />
          <h3 className="text-xl font-semibold text-gray-800">📋 综合投放策略</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-6">
            <h4 className="font-semibold text-orange-800 mb-4">🎯 主播合作策略</h4>
            <ul className="space-y-3 text-orange-700">
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span><strong>重点合作</strong>: {authorAnalysis.slice(0, 3).map(a => a.author).join('、')}</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span><strong>测试合作</strong>: {authorAnalysis.slice(3, 6).map(a => a.author).join('、')}</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span><strong>预算分配</strong>: TOP3主播分配60%预算，测试主播分配25%，剩余15%用于新主播发掘</span>
              </li>
            </ul>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-teal-50 rounded-xl p-6">
            <h4 className="font-semibold text-green-800 mb-4">📝 内容创作指南</h4>
            <ul className="space-y-3 text-green-700">
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span><strong>核心形式</strong>: 实用教程(45%) + 真实体验(30%) + 产品评测(25%)</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span><strong>内容时长</strong>: 3-5分钟短视频为主，配合深度图文内容</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span><strong>发布节奏</strong>: 每周2-3篇核心内容，保持用户期待感</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-6 bg-blue-50 rounded-xl p-6">
          <h4 className="font-semibold text-blue-800 mb-3">📊 预期ROI分析</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-white rounded-lg">
              <div className="text-2xl font-bold text-green-600">+45%</div>
              <div className="text-sm text-gray-600">互动提升</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg">
              <div className="text-2xl font-bold text-blue-600">+60%</div>
              <div className="text-sm text-gray-600">内容效果</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg">
              <div className="text-2xl font-bold text-purple-600">-30%</div>
              <div className="text-sm text-gray-600">投放风险</div>
            </div>
            <div className="text-center p-3 bg-white rounded-lg">
              <div className="text-2xl font-bold text-orange-600">2.8x</div>
              <div className="text-sm text-gray-600">ROI回报</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}