'use client'

import { useState } from 'react'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts'
import { Download, Filter, BarChart3, Hash, MapPin, Calendar } from 'lucide-react'
import { NoteData } from '@/app/page'

interface DashboardProps {
  data: NoteData[]
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

export default function Dashboard({ data }: DashboardProps) {
  const [activeTab, setActiveTab] = useState('overview')

  // Process data for charts
  const engagementData = data
    .sort((a, b) => b.engagement - a.engagement)
    .slice(0, 10)
    .map(note => ({
      name: note.title.length > 20 ? note.title.substring(0, 20) + '...' : note.title,
      engagement: note.engagement,
      likes: note.likes,
      collects: note.collects
    }))

  const locationData = Object.entries(
    data.reduce((acc, note) => {
      acc[note.location] = (acc[note.location] || 0) + 1
      return acc
    }, {} as Record<string, number>)
  ).map(([name, value]) => ({ name, value }))

  const tagData = Object.entries(
    data.flatMap(note => {
      if (!note.tags) return []
      
      // Handle array of tags (each could be string or comma-separated)
      if (Array.isArray(note.tags)) {
        return note.tags.flatMap(tag => {
          if (typeof tag === 'string') {
            return tag.split(',').map(t => t.trim()).filter(Boolean)
          }
          return []
        })
      }
      
      // Handle single string (could be comma-separated)
      if (typeof note.tags === 'string') {
        return note.tags.split(',').map(t => t.trim()).filter(Boolean)
      }
      
      return []
    }).reduce((acc, tag) => {
      if (tag) acc[tag] = (acc[tag] || 0) + 1
      return acc
    }, {} as Record<string, number>)
  )
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .map(([name, value]) => ({ name, value }))

  const timeSeriesData = data
    .filter(note => note.date)
    .reduce((acc, note) => {
      const date = note.date.split(' ')[0] // Get date part only
      acc[date] = (acc[date] || 0) + 1
      return acc
    }, {} as Record<string, number>)

  const timeSeriesChartData = Object.entries(timeSeriesData)
    .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
    .map(([date, count]) => ({ date, count }))

  // Calculate statistics
  const stats = {
    avgLikes: Math.round(data.reduce((sum, note) => sum + note.likes, 0) / data.length),
    avgCollects: Math.round(data.reduce((sum, note) => sum + note.collects, 0) / data.length),
    avgComments: Math.round(data.reduce((sum, note) => sum + note.comments, 0) / data.length),
    maxEngagement: Math.max(...data.map(note => note.engagement)),
    topAuthor: Object.entries(
      data.reduce((acc, note) => {
        acc[note.author] = (acc[note.author] || 0) + note.engagement
        return acc
      }, {} as Record<string, number>)
    ).sort((a, b) => b[1] - a[1])[0]
  }

  const exportToPDF = async () => {
    // This would be implemented with jsPDF and html2canvas
    alert('PDF导出功能将在完整版本中实现')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <BarChart3 className="w-6 h-6 text-blue-500" />
          <h2 className="text-2xl font-bold text-gray-800">数据分析仪表板</h2>
        </div>
        <button
          onClick={exportToPDF}
          className="flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600"
        >
          <Download className="w-4 h-4" />
          <span>导出PDF报告</span>
        </button>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        {[
          { id: 'overview', label: '总览', icon: BarChart3 },
          { id: 'locations', label: '地域分析', icon: MapPin },
          { id: 'tags', label: '标签分析', icon: Hash },
          { id: 'timeline', label: '时间趋势', icon: Calendar }
        ].map(({ id, label, icon: Icon }) => (
          <button
            key={id}
            onClick={() => setActiveTab(id)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === id
                ? 'bg-white text-gray-800 shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            <Icon className="w-4 h-4" />
            <span>{label}</span>
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Engagement Chart */}
        <div className="bg-white rounded-xl p-6 shadow-lg border">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">热门笔记互动排名</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={engagementData} layout="vertical">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis 
                  type="category" 
                  dataKey="name" 
                  width={120}
                  tick={{ fontSize: 12 }}
                />
                <Tooltip 
                  formatter={(value: number) => [value, '互动数']}
                  labelStyle={{ color: '#374151' }}
                />
                <Bar dataKey="engagement" fill="#3B82F6" radius={[0, 4, 4, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Location Distribution */}
        <div className="bg-white rounded-xl p-6 shadow-lg border">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">地域分布</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={locationData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {locationData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value: number) => [value, '笔记数']} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Tag Analysis */}
        <div className="bg-white rounded-xl p-6 shadow-lg border">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">热门标签</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={tagData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="name" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  tick={{ fontSize: 12 }}
                />
                <YAxis />
                <Tooltip formatter={(value: number) => [value, '出现次数']} />
                <Bar dataKey="value" fill="#10B981" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Time Series */}
        <div className="bg-white rounded-xl p-6 shadow-lg border">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">发布时间趋势</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={timeSeriesChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date" 
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="count" 
                  stroke="#F59E0B" 
                  strokeWidth={2}
                  dot={{ fill: '#F59E0B', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg p-4 shadow border">
          <p className="text-sm text-gray-600">平均点赞数</p>
          <p className="text-2xl font-bold text-gray-800">{stats.avgLikes}</p>
        </div>
        <div className="bg-white rounded-lg p-4 shadow border">
          <p className="text-sm text-gray-600">平均收藏数</p>
          <p className="text-2xl font-bold text-gray-800">{stats.avgCollects}</p>
        </div>
        <div className="bg-white rounded-lg p-4 shadow border">
          <p className="text-sm text-gray-600">平均评论数</p>
          <p className="text-2xl font-bold text-gray-800">{stats.avgComments}</p>
        </div>
        <div className="bg-white rounded-lg p-4 shadow border">
          <p className="text-sm text-gray-600">最高互动笔记</p>
          <p className="text-lg font-bold text-gray-800">{stats.maxEngagement}</p>
        </div>
      </div>

      {/* Top Content */}
      <div className="bg-white rounded-xl p-6 shadow-lg border">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">高互动内容示例</h3>
        <div className="space-y-3">
          {data
            .sort((a, b) => b.engagement - a.engagement)
            .slice(0, 5)
            .map((note, index) => (
              <div key={note.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-800 truncate">
                    {index + 1}. {note.title}
                  </p>
                  <p className="text-xs text-gray-500">
                    {note.author} • {note.location} • {new Date(note.date).toLocaleDateString()}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-blue-600">{note.engagement} 互动</p>
                  <p className="text-xs text-gray-500">
                    {note.likes}赞 • {note.collects}藏 • {note.comments}评
                  </p>
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  )
}