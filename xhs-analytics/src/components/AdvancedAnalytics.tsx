'use client'

import { useState, useMemo } from 'react'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ScatterChart, Scatter, LineChart, Line, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts'
import { TrendingUp, Target, Users, Zap, Award, AlertTriangle, CheckCircle, DollarSign } from 'lucide-react'
import { NoteData } from '@/app/page'

interface AdvancedAnalyticsProps {
  data: NoteData[]
}

// 内容效果评分模型
interface ContentPerformance {
  id: string
  title: string
  author: string
  engagementRate: number
  viralityScore: number
  contentQualityScore: number
  audienceAlignment: number
  conversionPotential: number
  optimalTiming: number
}

// 博主价值评估
interface InfluencerValue {
  author: string
  influenceScore: number
  commercialValue: number
  brandSafety: number
  growthTrend: number
  audienceQuality: number
  cpm: number
  roi: number
}

export default function AdvancedAnalytics({ data }: AdvancedAnalyticsProps) {
  const [activeTab, setActiveTab] = useState('content-optimization')

  // 内容效果分析
  const contentPerformance = useMemo(() => {
    return data.map(note => {
      const totalEngagement = note.likes + note.collects + note.comments
      const engagementRate = totalEngagement / Math.max(note.followers, 1) * 100
      
      // 病毒传播系数 (基于互动比例)
      const viralityScore = (note.likes * 0.3 + note.collects * 0.5 + note.comments * 0.2) / Math.max(totalEngagement, 1) * 100
      
      // 内容质量评分 (基于标题长度、标签数量等)
      const titleQuality = Math.min(note.title.length / 30, 1) * 100
      const tagQuality = Math.min((note.tags?.length || 0) / 5, 1) * 100
      const contentQualityScore = (titleQuality + tagQuality) / 2
      
      // 受众匹配度 (基于粉丝数和互动比例)
      const audienceAlignment = Math.min(totalEngagement / Math.max(note.followers * 0.05, 1), 1) * 100
      
      // 转化潜力 (收藏占比高说明转化意向强)
      const conversionPotential = (note.collects / Math.max(totalEngagement, 1)) * 100
      
      // 时机优化分数 (基于发布时间)
      const hour = new Date(note.date).getHours()
      const optimalTiming = hour >= 19 && hour <= 22 ? 100 : hour >= 12 && hour <= 14 ? 80 : 60

      return {
        id: note.id,
        title: note.title,
        author: note.author,
        engagementRate,
        viralityScore,
        contentQualityScore,
        audienceAlignment,
        conversionPotential,
        optimalTiming
      }
    }).sort((a, b) => b.engagementRate - a.engagementRate)
  }, [data])

  // 博主价值分析
  const influencerAnalysis = useMemo(() => {
    const authorStats = data.reduce((acc, note) => {
      if (!acc[note.author]) {
        acc[note.author] = {
          posts: [],
          totalEngagement: 0,
          totalFollowers: note.followers,
          totalLikes: 0,
          totalCollects: 0,
          totalComments: 0
        }
      }
      
      acc[note.author].posts.push(note)
      acc[note.author].totalEngagement += note.engagement
      acc[note.author].totalLikes += note.likes
      acc[note.author].totalCollects += note.collects
      acc[note.author].totalComments += note.comments
      
      return acc
    }, {} as Record<string, any>)

    return Object.entries(authorStats).map(([author, stats]) => {
      const avgEngagement = stats.totalEngagement / stats.posts.length
      const engagementRate = avgEngagement / Math.max(stats.totalFollowers, 1) * 100
      
      // 影响力评分 (粉丝数 + 平均互动)
      const influenceScore = Math.min((stats.totalFollowers / 10000 + avgEngagement / 1000) * 10, 100)
      
      // 商业价值 (基于收藏率，收藏率高说明转化价值高)
      const commercialValue = (stats.totalCollects / Math.max(stats.totalEngagement, 1)) * 100
      
      // 品牌安全度 (基于内容一致性)
      const brandSafety = stats.posts.length >= 3 ? 85 : 70
      
      // 增长趋势 (简化计算)
      const growthTrend = engagementRate > 5 ? 80 : engagementRate > 2 ? 60 : 40
      
      // 受众质量 (评论互动比例)
      const audienceQuality = (stats.totalComments / Math.max(stats.totalEngagement, 1)) * 100
      
      // CPM估算 (每千次曝光成本)
      const estimatedReach = stats.totalFollowers * 0.1 // 假设10%触达率
      const cpm = estimatedReach > 0 ? (avgEngagement * 0.1) / (estimatedReach / 1000) : 0
      
      // ROI估算
      const roi = commercialValue * influenceScore / 100

      return {
        author,
        influenceScore,
        commercialValue,
        brandSafety,
        growthTrend,
        audienceQuality,
        cpm: Math.round(cpm * 100) / 100,
        roi: Math.round(roi * 100) / 100,
        posts: stats.posts.length,
        avgEngagement: Math.round(avgEngagement),
        followers: stats.totalFollowers
      }
    }).sort((a, b) => b.roi - a.roi)
  }, [data])

  // 选题机会分析
  const topicOpportunities = useMemo(() => {
    const tagStats = data.reduce((acc, note) => {
      if (!note.tags) return acc
      
      const tags = Array.isArray(note.tags) ? note.tags : [note.tags]
      tags.forEach(tag => {
        if (typeof tag === 'string') {
          const cleanTag = tag.trim()
          if (!acc[cleanTag]) {
            acc[cleanTag] = {
              count: 0,
              totalEngagement: 0,
              avgEngagement: 0,
              competition: 0,
              opportunity: 0
            }
          }
          acc[cleanTag].count++
          acc[cleanTag].totalEngagement += note.engagement
        }
      })
      return acc
    }, {} as Record<string, any>)

    return Object.entries(tagStats)
      .map(([tag, stats]) => {
        stats.avgEngagement = stats.totalEngagement / stats.count
        stats.competition = stats.count > 10 ? 'high' : stats.count > 5 ? 'medium' : 'low'
        stats.opportunity = stats.avgEngagement / Math.max(stats.count, 1) * 10
        return { tag, ...stats }
      })
      .sort((a, b) => b.opportunity - a.opportunity)
      .slice(0, 20)
  }, [data])

  // 投放时机分析
  const timingAnalysis = useMemo(() => {
    const hourStats = Array.from({ length: 24 }, (_, hour) => {
      const hourData = data.filter(note => new Date(note.date).getHours() === hour)
      const avgEngagement = hourData.length > 0 
        ? hourData.reduce((sum, note) => sum + note.engagement, 0) / hourData.length 
        : 0
      
      return {
        hour,
        posts: hourData.length,
        avgEngagement: Math.round(avgEngagement),
        recommendation: avgEngagement > 1000 ? 'optimal' : avgEngagement > 500 ? 'good' : 'poor'
      }
    })

    return hourStats
  }, [data])

  const renderContentOptimization = () => (
    <div className="space-y-6">
      {/* 顶部指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg p-4 shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">平均互动率</p>
              <p className="text-2xl font-bold text-blue-600">
                {(contentPerformance.reduce((sum, item) => sum + item.engagementRate, 0) / contentPerformance.length).toFixed(2)}%
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg p-4 shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">病毒传播潜力</p>
              <p className="text-2xl font-bold text-green-600">
                {(contentPerformance.reduce((sum, item) => sum + item.viralityScore, 0) / contentPerformance.length).toFixed(1)}
              </p>
            </div>
            <Zap className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg p-4 shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">转化潜力</p>
              <p className="text-2xl font-bold text-purple-600">
                {(contentPerformance.reduce((sum, item) => sum + item.conversionPotential, 0) / contentPerformance.length).toFixed(1)}%
              </p>
            </div>
            <Target className="w-8 h-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg p-4 shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">内容质量</p>
              <p className="text-2xl font-bold text-orange-600">
                {(contentPerformance.reduce((sum, item) => sum + item.contentQualityScore, 0) / contentPerformance.length).toFixed(1)}
              </p>
            </div>
            <Award className="w-8 h-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* 内容效果排行 */}
      <div className="bg-white rounded-xl p-6 shadow-lg border">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">高效内容分析</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <ScatterChart data={contentPerformance.slice(0, 20)}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="engagementRate" 
                name="互动率"
                label={{ value: '互动率 (%)', position: 'insideBottom', offset: -5 }}
              />
              <YAxis 
                dataKey="conversionPotential" 
                name="转化潜力"
                label={{ value: '转化潜力 (%)', angle: -90, position: 'insideLeft' }}
              />
              <Tooltip 
                formatter={(value, name) => [value, name === 'engagementRate' ? '互动率' : '转化潜力']}
                labelFormatter={(label) => `${contentPerformance.find(item => item.engagementRate === label)?.title?.substring(0, 30)}...`}
              />
              <Scatter dataKey="engagementRate" fill="#3B82F6" />
            </ScatterChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* 选题机会分析 */}
      <div className="bg-white rounded-xl p-6 shadow-lg border">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">选题机会分析</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-700 mb-3">高机会标签</h4>
            <div className="space-y-2">
              {topicOpportunities.slice(0, 8).map((topic, index) => (
                <div key={topic.tag} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <span className="text-sm font-medium">{topic.tag}</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">竞争: {topic.competition}</span>
                    <span className="text-sm font-bold text-green-600">
                      {topic.opportunity.toFixed(1)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={topicOpportunities.slice(0, 10)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="tag" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  fontSize={12}
                />
                <YAxis />
                <Tooltip />
                <Bar dataKey="opportunity" fill="#10B981" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  )

  const renderInfluencerAnalysis = () => (
    <div className="space-y-6">
      {/* 博主价值排行 */}
      <div className="bg-white rounded-xl p-6 shadow-lg border">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">博主价值评估</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left p-2">博主</th>
                <th className="text-left p-2">影响力</th>
                <th className="text-left p-2">商业价值</th>
                <th className="text-left p-2">ROI</th>
                <th className="text-left p-2">CPM</th>
                <th className="text-left p-2">品牌安全</th>
                <th className="text-left p-2">推荐度</th>
              </tr>
            </thead>
            <tbody>
              {influencerAnalysis.slice(0, 10).map((influencer, index) => (
                <tr key={influencer.author} className="border-b hover:bg-gray-50">
                  <td className="p-2">
                    <div>
                      <div className="font-medium">{influencer.author}</div>
                      <div className="text-xs text-gray-500">
                        {influencer.followers.toLocaleString()} 粉丝 · {influencer.posts} 篇
                      </div>
                    </div>
                  </td>
                  <td className="p-2">
                    <div className="flex items-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full" 
                          style={{ width: `${influencer.influenceScore}%` }}
                        />
                      </div>
                      <span className="text-xs">{influencer.influenceScore.toFixed(0)}</span>
                    </div>
                  </td>
                  <td className="p-2">
                    <div className="flex items-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full" 
                          style={{ width: `${influencer.commercialValue}%` }}
                        />
                      </div>
                      <span className="text-xs">{influencer.commercialValue.toFixed(0)}</span>
                    </div>
                  </td>
                  <td className="p-2 font-medium text-purple-600">{influencer.roi}</td>
                  <td className="p-2">¥{influencer.cpm}</td>
                  <td className="p-2">
                    <span className={`px-2 py-1 rounded text-xs ${
                      influencer.brandSafety > 80 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {influencer.brandSafety > 80 ? '安全' : '一般'}
                    </span>
                  </td>
                  <td className="p-2">
                    {influencer.roi > 50 ? (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    ) : influencer.roi > 20 ? (
                      <AlertTriangle className="w-5 h-5 text-yellow-500" />
                    ) : (
                      <AlertTriangle className="w-5 h-5 text-red-500" />
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 博主类型分布 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-lg border">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">博主分类建议</h3>
          <div className="space-y-4">
            {[
              { type: '头部博主', count: influencerAnalysis.filter(i => i.followers > 100000).length, color: 'bg-red-500' },
              { type: '腰部博主', count: influencerAnalysis.filter(i => i.followers > 10000 && i.followers <= 100000).length, color: 'bg-yellow-500' },
              { type: '尾部博主', count: influencerAnalysis.filter(i => i.followers > 1000 && i.followers <= 10000).length, color: 'bg-green-500' },
              { type: '素人博主', count: influencerAnalysis.filter(i => i.followers <= 1000).length, color: 'bg-blue-500' }
            ].map(category => (
              <div key={category.type} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-4 h-4 rounded ${category.color} mr-3`} />
                  <span className="font-medium">{category.type}</span>
                </div>
                <span className="text-gray-600">{category.count} 人</span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-lg border">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">投放时机优化</h3>
          <div className="h-48">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={timingAnalysis}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="avgEngagement" 
                  stroke="#3B82F6" 
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        {[
          { id: 'content-optimization', label: '内容优化', icon: Target },
          { id: 'influencer-analysis', label: '博主分析', icon: Users }
        ].map(({ id, label, icon: Icon }) => (
          <button
            key={id}
            onClick={() => setActiveTab(id)}
            className={`flex items-center space-x-2 px-6 py-3 rounded-md text-sm font-medium transition-colors ${
              activeTab === id
                ? 'bg-white text-gray-800 shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            <Icon className="w-4 h-4" />
            <span>{label}</span>
          </button>
        ))}
      </div>

      {/* Content */}
      {activeTab === 'content-optimization' && renderContentOptimization()}
      {activeTab === 'influencer-analysis' && renderInfluencerAnalysis()}
    </div>
  )
}