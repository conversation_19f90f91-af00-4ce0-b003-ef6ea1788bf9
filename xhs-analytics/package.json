{"name": "xhs-analytics", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@types/xlsx": "^0.0.35", "axios": "^1.11.0", "date-fns": "^4.1.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.2", "lucide-react": "^0.542.0", "next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^3.1.2", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "typescript": "^5"}}