#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书软广分析系统 - Web界面版
支持Excel文件上传和在线分析
"""

import streamlit as st
import pandas as pd
import numpy as np
from collections import Counter
import plotly.express as px
import plotly.graph_objects as go
from xiaohongshu_smart_analyzer import SmartXHSAnalyzer

# 页面配置
st.set_page_config(
    page_title="小红书软广分析系统",
    page_icon="📊",
    layout="wide"
)

# 自定义CSS
st.markdown("""
<style>
.main-header {
    font-size: 2.5rem;
    color: #FF6B6B;
    text-align: center;
    margin-bottom: 2rem;
}
.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1rem;
    border-radius: 10px;
    color: white;
    text-align: center;
}
.insight-box {
    background: #f8f9fa;
    padding: 1rem;
    border-left: 4px solid #FF6B6B;
    margin: 1rem 0;
}
</style>
""", unsafe_allow_html=True)

def main():
    # 标题
    st.markdown('<h1 class="main-header">📊 小红书软广数据分析系统</h1>', unsafe_allow_html=True)
    
    # 侧边栏
    st.sidebar.title("🔧 分析工具")
    st.sidebar.markdown("---")
    
    # 文件上传
    uploaded_file = st.sidebar.file_uploader(
        "上传Excel文件",
        type=['xlsx', 'xls'],
        help="请上传包含小红书软广数据的Excel文件"
    )
    
    if uploaded_file is not None:
        # 初始化分析器
        analyzer = SmartXHSAnalyzer()
        
        # 保存上传的文件
        with open("temp_data.xlsx", "wb") as f:
            f.write(uploaded_file.getbuffer())
        
        # 加载数据
        if analyzer.load_data("temp_data.xlsx"):
            analyzer.preprocess_data()
            
            # 主要内容区域
            tab1, tab2, tab3, tab4 = st.tabs(["📈 数据概览", "🎯 投放策略", "💡 内容优化", "📊 详细分析"])
            
            with tab1:
                show_data_overview(analyzer)
            
            with tab2:
                show_investment_strategy(analyzer)
            
            with tab3:
                show_content_optimization(analyzer)
            
            with tab4:
                show_detailed_analysis(analyzer)
    
    else:
        # 显示使用说明
        show_instructions()

def show_data_overview(analyzer):
    """显示数据概览"""
    st.header("📈 数据概览")
    
    df = analyzer.processed_df
    
    # 核心指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("总投放内容", f"{len(df)} 篇")
    
    with col2:
        st.metric("合作博主数", f"{df['软文作者'].nunique()} 人")
    
    with col3:
        st.metric("平均互动率", f"{df['互动率'].mean():.1f}%")
    
    with col4:
        st.metric("总互动数", f"{df['总互动数'].sum():,}")
    
    # 数据分布图表
    col1, col2 = st.columns(2)
    
    with col1:
        # 博主类型分布
        blogger_dist = df['博主类型'].value_counts()
        fig = px.pie(
            values=blogger_dist.values,
            names=blogger_dist.index,
            title="博主类型分布"
        )
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # 内容类型分布
        content_dist = df['内容类型'].value_counts()
        fig = px.bar(
            x=content_dist.index,
            y=content_dist.values,
            title="内容类型分布"
        )
        st.plotly_chart(fig, use_container_width=True)
    
    # 互动率分布
    fig = px.histogram(
        df,
        x='互动率',
        nbins=20,
        title="互动率分布",
        labels={'互动率': '互动率 (%)', 'count': '内容数量'}
    )
    st.plotly_chart(fig, use_container_width=True)

def show_investment_strategy(analyzer):
    """显示投放策略"""
    st.header("🎯 投放策略建议")
    
    insights = analyzer.get_investment_insights()
    df = analyzer.processed_df
    
    # 策略建议卡片
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown(f"""
        <div class="insight-box">
        <h4>🏆 最佳博主类型</h4>
        <p><strong>{insights['博主选择策略']['最佳ROI博主类型']}</strong></p>
        <p>平均互动率最高，投放性价比最佳</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown(f"""
        <div class="insight-box">
        <h4>📝 最佳内容类型</h4>
        <p><strong>{insights['内容类型策略']['最佳内容类型']}</strong></p>
        <p>用户参与度最高的内容形式</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown(f"""
        <div class="insight-box">
        <h4>💭 最佳标题情感</h4>
        <p><strong>{insights['标题情感策略']['最佳情感类型']}</strong></p>
        <p>最能引起用户共鸣的情感表达</p>
        </div>
        """, unsafe_allow_html=True)
    
    # 详细对比图表
    st.subheader("📊 各维度效果对比")
    
    # 博主类型效果对比
    blogger_performance = df.groupby('博主类型')['互动率'].mean().sort_values(ascending=False)
    fig = px.bar(
        x=blogger_performance.index,
        y=blogger_performance.values,
        title="不同博主类型平均互动率对比",
        labels={'x': '博主类型', 'y': '平均互动率 (%)'}
    )
    st.plotly_chart(fig, use_container_width=True)
    
    # 内容类型效果对比
    content_performance = df.groupby('内容类型')['互动率'].mean().sort_values(ascending=False)
    fig = px.bar(
        x=content_performance.index,
        y=content_performance.values,
        title="不同内容类型平均互动率对比",
        labels={'x': '内容类型', 'y': '平均互动率 (%)'}
    )
    st.plotly_chart(fig, use_container_width=True)

def show_content_optimization(analyzer):
    """显示内容优化建议"""
    st.header("💡 内容优化建议")
    
    df = analyzer.processed_df
    tips = analyzer.get_content_optimization_tips()
    
    # 高效内容案例
    st.subheader("🏆 高效内容案例分析")
    
    top_content = df.nlargest(5, '互动率')
    
    for idx, (_, row) in enumerate(top_content.iterrows()):
        with st.expander(f"案例 {idx+1}: 《{row['软文标题']}》(互动率: {row['互动率']:.1f}%)"):
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**基本信息:**")
                st.write(f"- 博主类型: {row['博主类型']}")
                st.write(f"- 内容类型: {row['内容类型']}")
                st.write(f"- 标题情感: {row['标题情感']}")
                st.write(f"- 粉丝数: {row['粉丝_数值']:,}")
            
            with col2:
                st.write("**互动数据:**")
                st.write(f"- 点赞数: {row['点赞数']:,}")
                st.write(f"- 收藏数: {row['收藏数_数值']:,}")
                st.write(f"- 评论数: {row['评论数']:,}")
                st.write(f"- 总互动: {row['总互动数']:,}")
    
    # 用户行为偏好
    st.subheader("👥 用户行为偏好分析")
    
    avg_like_ratio = df['点赞占比'].mean()
    avg_collect_ratio = df['收藏占比'].mean()
    avg_comment_ratio = df['评论占比'].mean()
    
    # 创建饼图
    fig = go.Figure(data=[go.Pie(
        labels=['点赞', '收藏', '评论'],
        values=[avg_like_ratio, avg_collect_ratio, avg_comment_ratio],
        hole=.3
    )])
    fig.update_layout(title="用户互动行为分布")
    st.plotly_chart(fig, use_container_width=True)
    
    # 优化建议
    st.subheader("🎯 具体优化建议")
    
    insights = analyzer.get_investment_insights()

    recommendations = [
        f"📝 **标题优化**: 使用{insights['标题情感策略']['最佳情感类型']}类型的情感表达",
        f"👥 **博主选择**: 优先选择{insights['博主选择策略']['最佳ROI博主类型']}，性价比最高",
        f"📖 **内容类型**: 重点投放{insights['内容类型策略']['最佳内容类型']}类内容",
        f"💬 **互动引导**: 重点引导点赞({avg_like_ratio:.1f}%)和收藏({avg_collect_ratio:.1f}%)行为",
        "🕐 **发布时机**: 根据目标用户活跃时间选择发布时间",
        "🏷️ **标签策略**: 使用热门且相关的标签提高曝光"
    ]
    
    for rec in recommendations:
        st.markdown(rec)

def show_detailed_analysis(analyzer):
    """显示详细分析"""
    st.header("📊 详细数据分析")
    
    df = analyzer.processed_df
    
    # 数据表格
    st.subheader("📋 原始数据预览")
    
    # 选择要显示的列
    display_columns = [
        '软文标题', '软文作者', '博主类型', '内容类型', '标题情感',
        '点赞数', '收藏数_数值', '评论数', '互动率', '粉丝_数值'
    ]
    
    st.dataframe(
        df[display_columns].round(2),
        use_container_width=True
    )
    
    # 相关性分析
    st.subheader("🔗 指标相关性分析")
    
    numeric_columns = ['点赞数', '收藏数_数值', '评论数', '互动率', '粉丝_数值', '标题长度', '内容长度']
    correlation_matrix = df[numeric_columns].corr()
    
    fig = px.imshow(
        correlation_matrix,
        text_auto=True,
        aspect="auto",
        title="指标相关性热力图"
    )
    st.plotly_chart(fig, use_container_width=True)
    
    # 下载分析报告
    st.subheader("📥 导出分析报告")
    
    if st.button("生成完整分析报告"):
        report = analyzer.generate_actionable_report()
        st.download_button(
            label="下载分析报告",
            data=report,
            file_name="xiaohongshu_analysis_report.txt",
            mime="text/plain"
        )

def show_instructions():
    """显示使用说明"""
    st.header("📖 使用说明")
    
    st.markdown("""
    ### 🎯 系统功能
    
    这是一个专业的小红书软广投放效果分析系统，帮助您：
    
    - 📊 **数据分析**: 全面分析投放数据，识别关键趋势
    - 🎯 **策略优化**: 基于数据提供精准的投放策略建议  
    - 💡 **内容优化**: 分析高效内容特征，指导内容创作
    - 📈 **效果预测**: 预测不同策略的投放效果
    
    ### 📋 数据格式要求
    
    请确保您的Excel文件包含以下列：
    
    | 列名 | 说明 | 示例 |
    |------|------|------|
    | 笔记链接 | 小红书笔记链接 | https://xiaohongshu.com/note/xxx |
    | 软文标题 | 笔记标题 | "澳门外卖推荐" |
    | 软文作者 | 博主昵称 | "美食博主小王" |
    | 软文内容 | 笔记正文内容 | "今天给大家推荐..." |
    | 点赞数 | 点赞数量 | 1250 |
    | 收藏数 | 收藏数量 | 380 |
    | 评论数 | 评论数量 | 95 |
    | 粉丝 | 博主粉丝数 | 58000 或 5.8万 |
    | IP属地 | 博主IP归属地 | "上海" |
    
    ### 🚀 开始分析
    
    1. 点击左侧"上传Excel文件"按钮
    2. 选择您的数据文件
    3. 系统将自动处理并生成分析报告
    4. 在不同标签页查看详细分析结果
    
    ### 💡 分析亮点
    
    - **智能内容分类**: 自动识别测评避雷、种草推荐等内容类型
    - **博主价值评估**: 基于互动率评估不同粉丝量级博主的投放价值
    - **情感分析**: 分析标题情感对用户互动的影响
    - **可视化报告**: 丰富的图表展示，直观易懂
    """)

if __name__ == "__main__":
    main()
